using Microsoft.Extensions.Logging;
using SmaTrendFollower.Models;
using System.Collections.Concurrent;
using System.Text.Json;
using StackExchange.Redis;

namespace SmaTrendFollower.Services;

/// <summary>
/// Production-ready slippage estimation service for modeling expected vs actual entry fill prices
/// Learns and applies average expected fill distance by symbol and spread
/// </summary>
public class SlippageEstimator : ISlippageEstimator
{
    private readonly ILogger<SlippageEstimator> _logger;
    private readonly IOptimizedRedisConnectionService _redisService;
    private readonly ITimeProvider _timeProvider;

    private SlippageEstimatorConfig _config;
    private SlippageTrackingStatus _status;
    private readonly ConcurrentDictionary<string, SlippageModel> _models;
    private readonly ConcurrentDictionary<string, OrderSubmissionRecord> _pendingOrders;
    private readonly ConcurrentDictionary<string, byte> _trackedSymbols; // Using as concurrent set
    private readonly SemaphoreSlim _operationLock;
    private bool _disposed;

    // Redis key patterns
    private const string SlippageStatsKeyPattern = "slippage:stats:{0}";
    private const string SlippageModelKeyPattern = "slippage:model:{0}";
    private const string SlippageEventsKeyPattern = "slippage:events:{0}";
    private const string PendingOrdersKeyPattern = "slippage:pending:{0}";

    // === Events ===
    
    public event EventHandler<SlippageUpdatedEventArgs>? SlippageUpdated;
    public event EventHandler<HighSlippageDetectedEventArgs>? HighSlippageDetected;
    public event EventHandler<SlippageModelRecalibratedEventArgs>? ModelRecalibrated;

    public SlippageEstimator(
        ILogger<SlippageEstimator> logger,
        IOptimizedRedisConnectionService redisService,
        ITimeProvider timeProvider)
    {
        _logger = logger;
        _redisService = redisService;
        _timeProvider = timeProvider;

        _config = new SlippageEstimatorConfig();
        _status = SlippageTrackingStatus.Stopped;
        _models = new ConcurrentDictionary<string, SlippageModel>();
        _pendingOrders = new ConcurrentDictionary<string, OrderSubmissionRecord>();
        _trackedSymbols = new ConcurrentDictionary<string, byte>(); // Using as concurrent set
        _operationLock = new SemaphoreSlim(1, 1);
    }

    // === Core Methods ===

    public async Task StartTrackingAsync(IEnumerable<string> symbols, CancellationToken cancellationToken = default)
    {
        await _operationLock.WaitAsync(cancellationToken);
        try
        {
            _status = SlippageTrackingStatus.Starting;
            var symbolList = symbols.ToList();
            
            _logger.LogInformation("Starting slippage tracking for {Count} symbols: {Symbols}",
                symbolList.Count, string.Join(", ", symbolList));

            // Load existing models from Redis
            foreach (var symbol in symbolList)
            {
                _trackedSymbols.TryAdd(symbol, 0); // Add to concurrent set
                await LoadSlippageModelAsync(symbol);
            }

            _status = SlippageTrackingStatus.Active;
            _logger.LogInformation("Slippage tracking started for {Count} symbols", symbolList.Count);
        }
        catch (Exception ex)
        {
            _status = SlippageTrackingStatus.Error;
            _logger.LogError(ex, "Failed to start slippage tracking");
            throw;
        }
        finally
        {
            _operationLock.Release();
        }
    }

    public async Task StopTrackingAsync(CancellationToken cancellationToken = default)
    {
        await _operationLock.WaitAsync(cancellationToken);
        try
        {
            _logger.LogInformation("Stopping slippage tracking");
            
            // Save all models to Redis before stopping
            foreach (var symbol in _trackedSymbols.Keys)
            {
                if (_models.TryGetValue(symbol, out var model))
                {
                    await SaveSlippageModelAsync(symbol, model);
                }
            }

            _trackedSymbols.Clear();
            _models.Clear();
            _pendingOrders.Clear();
            _status = SlippageTrackingStatus.Stopped;
            
            _logger.LogInformation("Slippage tracking stopped");
        }
        catch (Exception ex)
        {
            _status = SlippageTrackingStatus.Error;
            _logger.LogError(ex, "Failed to stop slippage tracking");
            throw;
        }
        finally
        {
            _operationLock.Release();
        }
    }

    public async Task RecordOrderSubmissionAsync(OrderSubmissionRecord submission)
    {
        if (_status != SlippageTrackingStatus.Active || !_trackedSymbols.ContainsKey(submission.Symbol))
            return;

        try
        {
            _pendingOrders[submission.OrderId] = submission;
            
            // Cache in Redis
            var database = await _redisService.GetDatabaseAsync();
            var key = string.Format(PendingOrdersKeyPattern, submission.OrderId);
            var json = JsonSerializer.Serialize(submission);
            await database.StringSetAsync(key, json, TimeSpan.FromHours(24));
            
            _logger.LogDebug("Recorded order submission for {Symbol}: {OrderId}", 
                submission.Symbol, submission.OrderId);
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Failed to record order submission for {OrderId}", submission.OrderId);
        }
    }

    public async Task RecordOrderFillAsync(OrderFillRecord fill)
    {
        if (_status != SlippageTrackingStatus.Active || !_trackedSymbols.ContainsKey(fill.Symbol))
            return;

        try
        {
            // Find corresponding submission
            if (!_pendingOrders.TryRemove(fill.OrderId, out var submission))
            {
                // Try to load from Redis
                var database = await _redisService.GetDatabaseAsync();
                var key = string.Format(PendingOrdersKeyPattern, fill.OrderId);
                var json = await database.StringGetAsync(key);
                
                if (json.HasValue)
                {
                    submission = JsonSerializer.Deserialize<OrderSubmissionRecord>(json!);
                    await database.KeyDeleteAsync(key);
                }
                else
                {
                    _logger.LogWarning("No submission record found for fill {OrderId}", fill.OrderId);
                    return;
                }
            }

            // Calculate slippage
            var slippage = CalculateSlippage(submission, fill);
            
            // Update statistics and model
            await UpdateSlippageStatisticsAsync(fill.Symbol, slippage);
            await UpdateSlippageModelAsync(fill.Symbol, slippage);
            
            // Check for high slippage
            if (Math.Abs(slippage.ActualSlippageBps) > _config.HighSlippageThreshold * 10000)
            {
                HighSlippageDetected?.Invoke(this, new HighSlippageDetectedEventArgs
                {
                    Symbol = fill.Symbol,
                    SlippageEvent = slippage,
                    ThresholdBps = _config.HighSlippageThreshold * 10000,
                    DetectionTime = _timeProvider.UtcNow
                });
            }
            
            _logger.LogDebug("Recorded slippage for {Symbol}: {SlippageBps} bps", 
                fill.Symbol, slippage.ActualSlippageBps);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to record order fill for {OrderId}", fill.OrderId);
        }
    }

    public async Task<SlippageEstimate> EstimateSlippageAsync(string symbol, decimal orderSize, OrderSide side, decimal currentSpread)
    {
        if (!_trackedSymbols.ContainsKey(symbol) || !_models.TryGetValue(symbol, out var model))
        {
            // Return default estimate if no model available
            return new SlippageEstimate(
                Symbol: symbol,
                ExpectedSlippageBps: 5.0m, // Default 0.5 bps
                ExpectedSlippageDollar: orderSize * 0.0005m,
                ConfidenceLevel: 0.1m,
                PrimaryReason: SlippageReason.Unknown,
                EstimateTime: _timeProvider.UtcNow,
                ModelMetrics: new SlippageModelMetrics(0.1m, 0, 0, 0, _timeProvider.UtcNow)
            );
        }

        try
        {
            // Calculate expected slippage using model
            var baseSlippage = model.BaseSlippageBps;
            var spreadAdjustment = _config.EnableSpreadAdjustment ? currentSpread * model.SpreadMultiplier : 0;
            var volumeAdjustment = _config.EnableVolumeAdjustment ? Math.Log10((double)orderSize) * (double)model.VolumeImpactFactor : 0;
            
            var expectedSlippageBps = baseSlippage + (decimal)spreadAdjustment + (decimal)volumeAdjustment + model.VolatilityAdjustment;
            var expectedSlippageDollar = orderSize * (expectedSlippageBps / 10000m);
            
            var reason = DetermineSlippageReason(currentSpread, orderSize, model);
            
            return new SlippageEstimate(
                Symbol: symbol,
                ExpectedSlippageBps: Math.Max(0, expectedSlippageBps),
                ExpectedSlippageDollar: expectedSlippageDollar,
                ConfidenceLevel: model.ModelAccuracy,
                PrimaryReason: reason,
                EstimateTime: _timeProvider.UtcNow,
                ModelMetrics: new SlippageModelMetrics(
                    Accuracy: model.ModelAccuracy,
                    MeanAbsoluteError: 0, // Would need historical data to calculate
                    RSquared: 0, // Would need historical data to calculate
                    DataPoints: model.TrainingDataPoints,
                    LastUpdate: model.LastCalibration
                )
            );
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to estimate slippage for {Symbol}", symbol);
            throw;
        }
    }

    public async Task<SlippageStatistics?> GetSlippageStatisticsAsync(string symbol)
    {
        try
        {
            var database = await _redisService.GetDatabaseAsync();
            var key = string.Format(SlippageStatsKeyPattern, symbol);
            var json = await database.StringGetAsync(key);
            
            if (!json.HasValue)
                return null;
                
            return JsonSerializer.Deserialize<SlippageStatistics>(json!);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get slippage statistics for {Symbol}", symbol);
            return null;
        }
    }

    public async Task<SlippageModel?> GetSlippageModelAsync(string symbol)
    {
        if (_models.TryGetValue(symbol, out var model))
            return model;
            
        return await LoadSlippageModelAsync(symbol);
    }

    public async Task RecalibrateModelAsync(string symbol)
    {
        if (!_trackedSymbols.ContainsKey(symbol))
            return;

        try
        {
            var oldModel = _models.GetValueOrDefault(symbol);
            
            // Get recent slippage events for recalibration
            var events = await GetRecentSlippageEventsAsync(symbol, _config.HistoryDays * 24);
            var eventList = events.ToList();
            
            if (eventList.Count < _config.MinSamplesForModel)
            {
                _logger.LogWarning("Insufficient data for model recalibration for {Symbol}: {Count} samples", 
                    symbol, eventList.Count);
                return;
            }

            // Calculate new model parameters
            var newModel = CalculateSlippageModel(symbol, eventList);
            _models[symbol] = newModel;
            
            await SaveSlippageModelAsync(symbol, newModel);
            
            if (oldModel != null)
            {
                ModelRecalibrated?.Invoke(this, new SlippageModelRecalibratedEventArgs
                {
                    Symbol = symbol,
                    OldModel = oldModel,
                    NewModel = newModel,
                    RecalibrationTime = _timeProvider.UtcNow
                });
            }
            
            _logger.LogInformation("Recalibrated slippage model for {Symbol}: {Accuracy:P2} accuracy with {DataPoints} data points",
                symbol, newModel.ModelAccuracy, newModel.TrainingDataPoints);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to recalibrate model for {Symbol}", symbol);
        }
    }

    public async Task<IEnumerable<SlippageEvent>> GetRecentSlippageEventsAsync(string symbol, int hours = 24)
    {
        try
        {
            var database = await _redisService.GetDatabaseAsync();
            var key = string.Format(SlippageEventsKeyPattern, symbol);
            var events = await database.ListRangeAsync(key, 0, -1);
            
            var cutoffTime = _timeProvider.UtcNow.AddHours(-hours);
            
            return events
                .Select(e => JsonSerializer.Deserialize<SlippageEvent>(e!))
                .Where(e => e.EventTime >= cutoffTime)
                .OrderByDescending(e => e.EventTime);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get recent slippage events for {Symbol}", symbol);
            return Enumerable.Empty<SlippageEvent>();
        }
    }

    // === Configuration ===

    public Task UpdateConfigurationAsync(SlippageEstimatorConfig config)
    {
        _config = config;
        _logger.LogInformation("Updated slippage estimator configuration");
        return Task.CompletedTask;
    }

    public SlippageTrackingStatus GetStatus() => _status;

    public IEnumerable<string> GetTrackedSymbols() => _trackedSymbols.Keys;

    // === Private Helper Methods ===

    private SlippageEvent CalculateSlippage(OrderSubmissionRecord submission, OrderFillRecord fill)
    {
        // Calculate expected price based on order side
        var expectedPrice = submission.Side == OrderSide.Buy ? submission.AskPrice : submission.BidPrice;
        var actualSlippageBps = ((fill.FillPrice - expectedPrice) / expectedPrice) * 10000;
        
        // Adjust sign for sell orders
        if (submission.Side == OrderSide.Sell)
            actualSlippageBps = -actualSlippageBps;
        
        var reason = DetermineSlippageReason(submission.Spread, submission.Quantity, null);
        
        return new SlippageEvent(
            OrderId: fill.OrderId,
            Symbol: fill.Symbol,
            Side: fill.Side,
            ActualSlippageBps: actualSlippageBps,
            ExpectedSlippageBps: 0, // Would need to look up previous estimate
            SlippageDifference: actualSlippageBps,
            EventTime: fill.FillTime,
            Reason: reason
        );
    }

    private SlippageReason DetermineSlippageReason(decimal spread, decimal orderSize, SlippageModel? model)
    {
        // Simple heuristic-based classification
        if (spread > 0.10m) return SlippageReason.WideSpread;
        if (orderSize > 10000m) return SlippageReason.LargeOrderSize;
        return SlippageReason.Normal;
    }

    private async Task UpdateSlippageStatisticsAsync(string symbol, SlippageEvent slippageEvent)
    {
        // Implementation would update running statistics
        // For brevity, this is a simplified version
        var stats = await GetSlippageStatisticsAsync(symbol) ?? new SlippageStatistics(
            Symbol: symbol,
            AverageSlippageBps: 0,
            MedianSlippageBps: 0,
            StandardDeviationBps: 0,
            MaxSlippageBps: 0,
            MinSlippageBps: 0,
            SampleCount: 0,
            LastUpdate: _timeProvider.UtcNow,
            AverageSpread: 0,
            AverageOrderSize: 0
        );

        // Update statistics (simplified)
        var newStats = stats with
        {
            AverageSlippageBps = (stats.AverageSlippageBps * stats.SampleCount + slippageEvent.ActualSlippageBps) / (stats.SampleCount + 1),
            SampleCount = stats.SampleCount + 1,
            LastUpdate = _timeProvider.UtcNow
        };

        // Save to Redis
        var database = await _redisService.GetDatabaseAsync();
        var key = string.Format(SlippageStatsKeyPattern, symbol);
        var json = JsonSerializer.Serialize(newStats);
        await database.StringSetAsync(key, json, _config.CacheExpiry);

        SlippageUpdated?.Invoke(this, new SlippageUpdatedEventArgs
        {
            Symbol = symbol,
            Statistics = newStats,
            UpdateTime = _timeProvider.UtcNow
        });
    }

    private async Task UpdateSlippageModelAsync(string symbol, SlippageEvent slippageEvent)
    {
        // Store slippage event
        var database = await _redisService.GetDatabaseAsync();
        var eventsKey = string.Format(SlippageEventsKeyPattern, symbol);
        var eventJson = JsonSerializer.Serialize(slippageEvent);
        
        await database.ListLeftPushAsync(eventsKey, eventJson);
        await database.ListTrimAsync(eventsKey, 0, 999); // Keep last 1000 events
        await database.KeyExpireAsync(eventsKey, TimeSpan.FromDays(_config.HistoryDays));
    }

    private SlippageModel CalculateSlippageModel(string symbol, List<SlippageEvent> events)
    {
        // Simplified model calculation
        var avgSlippage = events.Average(e => e.ActualSlippageBps);
        var accuracy = Math.Max(0.1m, 1.0m - (events.StandardDeviation(e => e.ActualSlippageBps) / Math.Max(1, Math.Abs(avgSlippage))));
        
        return new SlippageModel(
            Symbol: symbol,
            BaseSlippageBps: avgSlippage,
            SpreadMultiplier: 0.5m,
            VolumeImpactFactor: 0.1m,
            VolatilityAdjustment: 0,
            ModelAccuracy: Math.Min(1.0m, accuracy),
            LastCalibration: _timeProvider.UtcNow,
            TrainingDataPoints: events.Count
        );
    }

    private async Task<SlippageModel?> LoadSlippageModelAsync(string symbol)
    {
        try
        {
            var database = await _redisService.GetDatabaseAsync();
            var key = string.Format(SlippageModelKeyPattern, symbol);
            var json = await database.StringGetAsync(key);
            
            if (!json.HasValue)
                return null;
                
            var model = JsonSerializer.Deserialize<SlippageModel>(json!);
            _models[symbol] = model;
            return model;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to load slippage model for {Symbol}", symbol);
            return null;
        }
    }

    private async Task SaveSlippageModelAsync(string symbol, SlippageModel model)
    {
        try
        {
            var database = await _redisService.GetDatabaseAsync();
            var key = string.Format(SlippageModelKeyPattern, symbol);
            var json = JsonSerializer.Serialize(model);
            await database.StringSetAsync(key, json, TimeSpan.FromDays(_config.HistoryDays));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to save slippage model for {Symbol}", symbol);
        }
    }

    public void Dispose()
    {
        if (_disposed) return;
        
        _operationLock?.Dispose();
        _disposed = true;
    }
}

// Extension method for standard deviation calculation
public static class EnumerableExtensions
{
    public static decimal StandardDeviation<T>(this IEnumerable<T> values, Func<T, decimal> selector)
    {
        var list = values.Select(selector).ToList();
        if (list.Count < 2) return 0;
        
        var avg = list.Average();
        var sumOfSquares = list.Sum(x => (x - avg) * (x - avg));
        return (decimal)Math.Sqrt((double)(sumOfSquares / (list.Count - 1)));
    }
}
