using FluentAssertions;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Moq;
using SmaTrendFollower.Models;
using SmaTrendFollower.Services;
using StackExchange.Redis;
using Xunit;

namespace SmaTrendFollower.Tests.Services;

public class DailyUniverseRefreshServiceTests : IDisposable
{
    private readonly Mock<IPolygonSymbolUniverseService> _mockSymbolUniverseService;
    private readonly Mock<IPolygonSymbolSnapshotService> _mockSnapshotService;
    private readonly Mock<IConfiguration> _mockConfiguration;
    private readonly Mock<ILogger<DailyUniverseRefreshService>> _mockLogger;
    private readonly Mock<IDatabase> _mockRedis;
    private readonly DailyUniverseRefreshService _service;

    public DailyUniverseRefreshServiceTests()
    {
        _mockSymbolUniverseService = new Mock<IPolygonSymbolUniverseService>();
        _mockSnapshotService = new Mock<IPolygonSymbolSnapshotService>();
        _mockConfiguration = new Mock<IConfiguration>();
        _mockLogger = new Mock<ILogger<DailyUniverseRefreshService>>();
        _mockRedis = new Mock<IDatabase>();

        // Setup configuration
        _mockConfiguration.Setup(c => c["REDIS_URL"]).Returns((string?)null);
        _mockConfiguration.Setup(c => c["REDIS_DATABASE"]).Returns("0");
        _mockConfiguration.Setup(c => c["REDIS_PASSWORD"]).Returns((string?)null);

        _service = new DailyUniverseRefreshService(
            _mockSymbolUniverseService.Object,
            _mockSnapshotService.Object,
            _mockConfiguration.Object,
            _mockLogger.Object);
    }

    [Fact]
    public async Task RefreshUniverseAsync_ShouldReturnCandidates_WhenServicesReturnValidData()
    {
        // Arrange
        var mockSymbols = new List<PolygonSymbolInfo>
        {
            new() { Ticker = "AAPL", Name = "Apple Inc.", Market = "stocks", Active = true, Type = "CS" },
            new() { Ticker = "MSFT", Name = "Microsoft Corp.", Market = "stocks", Active = true, Type = "CS" },
            new() { Ticker = "GOOGL", Name = "Alphabet Inc.", Market = "stocks", Active = true, Type = "CS" }
        };

        var mockCandidates = new List<UniverseCandidate>
        {
            new()
            {
                Symbol = "AAPL",
                Price = 150.00m,
                AverageVolume = 50_000_000,
                VolatilityPercent = 25.0m,
                RankingScore = 100.0m,
                LastUpdated = DateTime.UtcNow
            },
            new()
            {
                Symbol = "MSFT",
                Price = 300.00m,
                AverageVolume = 30_000_000,
                VolatilityPercent = 20.0m,
                RankingScore = 95.0m,
                LastUpdated = DateTime.UtcNow
            }
        };

        _mockSymbolUniverseService
            .Setup(s => s.GetSymbolListAsync(It.IsAny<CancellationToken>()))
            .ReturnsAsync(mockSymbols);

        _mockSnapshotService
            .Setup(s => s.FilterAndRankSymbolsAsync(
                It.IsAny<IEnumerable<PolygonSymbolInfo>>(),
                It.IsAny<CandidateFilterCriteria>(),
                It.IsAny<CancellationToken>()))
            .ReturnsAsync(mockCandidates);

        // Act
        var result = await _service.RefreshUniverseAsync();

        // Assert
        result.Should().NotBeNull();
        result.Candidates.Should().HaveCount(2);
        result.Candidates.Should().Contain(c => c.Symbol == "AAPL");
        result.Candidates.Should().Contain(c => c.Symbol == "MSFT");
        result.EvaluatedCount.Should().Be(3);
        result.CandidateCount.Should().Be(2);
        result.GeneratedAt.Should().BeCloseTo(DateTime.UtcNow, TimeSpan.FromMinutes(1));
    }

    [Fact]
    public async Task RefreshUniverseAsync_ShouldPreventConcurrentRefresh()
    {
        // Arrange
        var mockSymbols = new List<PolygonSymbolInfo>
        {
            new() { Ticker = "AAPL", Name = "Apple Inc.", Market = "stocks", Active = true, Type = "CS" }
        };

        var mockCandidates = new List<UniverseCandidate>
        {
            new()
            {
                Symbol = "AAPL",
                Price = 150.00m,
                AverageVolume = 50_000_000,
                VolatilityPercent = 25.0m,
                RankingScore = 100.0m,
                LastUpdated = DateTime.UtcNow
            }
        };

        _mockSymbolUniverseService
            .Setup(s => s.GetSymbolListAsync(It.IsAny<CancellationToken>()))
            .ReturnsAsync(mockSymbols);

        _mockSnapshotService
            .Setup(s => s.FilterAndRankSymbolsAsync(
                It.IsAny<IEnumerable<PolygonSymbolInfo>>(),
                It.IsAny<CandidateFilterCriteria>(),
                It.IsAny<CancellationToken>()))
            .ReturnsAsync(mockCandidates);

        // Act - Start two concurrent refresh operations
        var task1 = _service.RefreshUniverseAsync();
        var task2 = _service.RefreshUniverseAsync();

        var results = await Task.WhenAll(task1, task2);

        // Assert
        results.Should().HaveCount(2);
        results[0].Should().NotBeNull();
        results[1].Should().NotBeNull();
        // Both should return valid results (second one should return current cached data)
    }

    [Fact]
    public async Task GetCurrentCandidatesAsync_ShouldReturnNull_WhenRedisNotAvailable()
    {
        // Act
        var result = await _service.GetCurrentCandidatesAsync();

        // Assert
        result.Should().BeNull();
    }

    [Fact]
    public async Task IsCacheValidAsync_ShouldReturnFalse_WhenNoCachedData()
    {
        // Act
        var result = await _service.IsCacheValidAsync();

        // Assert
        result.Should().BeFalse();
    }

    [Fact]
    public async Task GetStatusAsync_ShouldReturnValidStatus()
    {
        // Act
        var result = await _service.GetStatusAsync();

        // Assert
        result.Should().NotBeNull();
        result.IsRefreshing.Should().BeFalse();
        result.ServiceStatus.Should().Be("Idle");
        result.CandidateCount.Should().Be(0);
    }

    [Fact]
    public async Task RefreshUniverseAsync_ShouldHandleSymbolServiceError_Gracefully()
    {
        // Arrange
        _mockSymbolUniverseService
            .Setup(s => s.GetSymbolListAsync(It.IsAny<CancellationToken>()))
            .ThrowsAsync(new Exception("Symbol service error"));

        // Act & Assert
        await Assert.ThrowsAsync<Exception>(() => _service.RefreshUniverseAsync());
    }

    [Fact]
    public async Task RefreshUniverseAsync_ShouldHandleSnapshotServiceError_Gracefully()
    {
        // Arrange
        var mockSymbols = new List<PolygonSymbolInfo>
        {
            new() { Ticker = "AAPL", Name = "Apple Inc.", Market = "stocks", Active = true, Type = "CS" }
        };

        _mockSymbolUniverseService
            .Setup(s => s.GetSymbolListAsync(It.IsAny<CancellationToken>()))
            .ReturnsAsync(mockSymbols);

        _mockSnapshotService
            .Setup(s => s.FilterAndRankSymbolsAsync(
                It.IsAny<IEnumerable<PolygonSymbolInfo>>(),
                It.IsAny<CandidateFilterCriteria>(),
                It.IsAny<CancellationToken>()))
            .ThrowsAsync(new Exception("Snapshot service error"));

        // Act & Assert
        await Assert.ThrowsAsync<Exception>(() => _service.RefreshUniverseAsync());
    }

    [Fact]
    public async Task RefreshUniverseAsync_ShouldApplyCorrectFilterCriteria()
    {
        // Arrange
        var mockSymbols = new List<PolygonSymbolInfo>
        {
            new() { Ticker = "AAPL", Name = "Apple Inc.", Market = "stocks", Active = true, Type = "CS" }
        };

        var mockCandidates = new List<UniverseCandidate>
        {
            new()
            {
                Symbol = "AAPL",
                Price = 150.00m,
                AverageVolume = 50_000_000,
                VolatilityPercent = 25.0m,
                RankingScore = 100.0m,
                LastUpdated = DateTime.UtcNow
            }
        };

        _mockSymbolUniverseService
            .Setup(s => s.GetSymbolListAsync(It.IsAny<CancellationToken>()))
            .ReturnsAsync(mockSymbols);

        _mockSnapshotService
            .Setup(s => s.FilterAndRankSymbolsAsync(
                It.IsAny<IEnumerable<PolygonSymbolInfo>>(),
                It.IsAny<CandidateFilterCriteria>(),
                It.IsAny<CancellationToken>()))
            .ReturnsAsync(mockCandidates);

        // Act
        var result = await _service.RefreshUniverseAsync();

        // Assert
        _mockSnapshotService.Verify(s => s.FilterAndRankSymbolsAsync(
            It.IsAny<IEnumerable<PolygonSymbolInfo>>(),
            It.Is<CandidateFilterCriteria>(c => 
                c.MinPrice == 10.0m &&
                c.MinAverageVolume == 1_000_000 &&
                c.MinVolatilityPercent == 2.0m &&
                c.MaxCandidates == 200),
            It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task RefreshUniverseAsync_ShouldIncludeMetrics_InResult()
    {
        // Arrange
        var mockSymbols = new List<PolygonSymbolInfo>
        {
            new() { Ticker = "AAPL", Name = "Apple Inc.", Market = "stocks", Active = true, Type = "CS" },
            new() { Ticker = "MSFT", Name = "Microsoft Corp.", Market = "stocks", Active = true, Type = "CS" }
        };

        var mockCandidates = new List<UniverseCandidate>
        {
            new()
            {
                Symbol = "AAPL",
                Price = 150.00m,
                AverageVolume = 50_000_000,
                VolatilityPercent = 25.0m,
                RankingScore = 100.0m,
                LastUpdated = DateTime.UtcNow
            }
        };

        _mockSymbolUniverseService
            .Setup(s => s.GetSymbolListAsync(It.IsAny<CancellationToken>()))
            .ReturnsAsync(mockSymbols);

        _mockSnapshotService
            .Setup(s => s.FilterAndRankSymbolsAsync(
                It.IsAny<IEnumerable<PolygonSymbolInfo>>(),
                It.IsAny<CandidateFilterCriteria>(),
                It.IsAny<CancellationToken>()))
            .ReturnsAsync(mockCandidates);

        // Act
        var result = await _service.RefreshUniverseAsync();

        // Assert
        result.Metrics.Should().NotBeNull();
        result.Metrics.GenerationTime.Should().BeGreaterThan(TimeSpan.Zero);
        result.Metrics.FilterBreakdown.Should().ContainKey("TotalSymbols");
        result.Metrics.FilterBreakdown.Should().ContainKey("QualifiedCandidates");
        result.Metrics.FilterBreakdown["TotalSymbols"].Should().Be(2);
        result.Metrics.FilterBreakdown["QualifiedCandidates"].Should().Be(1);
        result.Metrics.DataFreshness.Should().ContainKey("SymbolList");
        result.Metrics.DataFreshness.Should().ContainKey("Snapshots");
    }

    public void Dispose()
    {
        _service?.Dispose();
    }
}
