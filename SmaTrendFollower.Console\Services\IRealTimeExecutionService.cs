using SmaTrendFollower.Models;

namespace SmaTrendFollower.Services;

/// <summary>
/// Real-time execution service that combines trade ticks + quote spread width + VWAP
/// Chooses limit vs market dynamically and throttles trade entry during high bid-ask instability or spread spikes
/// </summary>
public interface IRealTimeExecutionService : IDisposable
{
    // === Events ===
    
    /// <summary>
    /// Fired when execution decision is made
    /// </summary>
    event EventHandler<ExecutionDecisionEventArgs>? ExecutionDecisionMade;
    
    /// <summary>
    /// Fired when trade execution is throttled
    /// </summary>
    event EventHandler<ExecutionThrottledEventArgs>? ExecutionThrottled;
    
    /// <summary>
    /// Fired when spread spike is detected
    /// </summary>
    event EventHandler<SpreadSpikeEventArgs>? SpreadSpikeDetected;
    
    /// <summary>
    /// Fired when execution conditions change
    /// </summary>
    event EventHandler<ExecutionConditionsChangedEventArgs>? ExecutionConditionsChanged;
    
    // === Core Methods ===
    
    /// <summary>
    /// Start real-time execution monitoring
    /// </summary>
    Task StartMonitoringAsync(IEnumerable<string> symbols, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Stop real-time execution monitoring
    /// </summary>
    Task StopMonitoringAsync(CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Evaluate execution strategy for a symbol
    /// </summary>
    Task<ExecutionStrategy> EvaluateExecutionStrategyAsync(string symbol, OrderSide side, decimal quantity, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Check if execution is currently allowed for a symbol
    /// </summary>
    Task<bool> IsExecutionAllowedAsync(string symbol, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Get optimal execution price for a symbol
    /// </summary>
    Task<decimal?> GetOptimalExecutionPriceAsync(string symbol, OrderSide side, decimal quantity, CancellationToken cancellationToken = default);
    
    // === Analysis Methods ===
    
    /// <summary>
    /// Get current market microstructure analysis
    /// </summary>
    Task<MarketMicrostructureAnalysis> GetMicrostructureAnalysisAsync(string symbol, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Get current spread analysis
    /// </summary>
    Task<SpreadAnalysis> GetSpreadAnalysisAsync(string symbol, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Get VWAP execution analysis
    /// </summary>
    Task<VwapExecutionAnalysis> GetVwapExecutionAnalysisAsync(string symbol, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Get execution quality metrics
    /// </summary>
    Task<ExecutionQualityMetrics> GetExecutionQualityMetricsAsync(string symbol, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Get liquidity assessment
    /// </summary>
    Task<LiquidityAssessment> GetLiquidityAssessmentAsync(string symbol, CancellationToken cancellationToken = default);
    
    // === Execution Control ===
    
    /// <summary>
    /// Temporarily throttle execution for a symbol
    /// </summary>
    Task ThrottleExecutionAsync(string symbol, TimeSpan duration, string reason);
    
    /// <summary>
    /// Remove execution throttle for a symbol
    /// </summary>
    Task RemoveExecutionThrottleAsync(string symbol);
    
    /// <summary>
    /// Get execution throttle status
    /// </summary>
    Task<ExecutionThrottleStatus?> GetExecutionThrottleStatusAsync(string symbol);
    
    /// <summary>
    /// Update execution parameters
    /// </summary>
    Task UpdateExecutionParametersAsync(string symbol, ExecutionParameters parameters);
    
    // === Status and Configuration ===
    
    /// <summary>
    /// Get current monitoring status
    /// </summary>
    RealTimeExecutionStatus GetStatus();
    
    /// <summary>
    /// Get monitored symbols
    /// </summary>
    IEnumerable<string> GetMonitoredSymbols();
    
    /// <summary>
    /// Update configuration
    /// </summary>
    Task UpdateConfigurationAsync(RealTimeExecutionConfig config);
    
    /// <summary>
    /// Get execution statistics
    /// </summary>
    Task<ExecutionStatistics> GetExecutionStatisticsAsync();
}

/// <summary>
/// Execution strategy recommendation
/// </summary>
public class ExecutionStrategy
{
    public string Symbol { get; set; } = string.Empty;
    public OrderType RecommendedOrderType { get; set; }
    public decimal? LimitPrice { get; set; }
    public ExecutionTiming Timing { get; set; }
    public ExecutionUrgency Urgency { get; set; }
    public decimal ConfidenceScore { get; set; }
    public string Reasoning { get; set; } = string.Empty;
    public Dictionary<string, object> Metadata { get; set; } = new();
}

/// <summary>
/// Market microstructure analysis
/// </summary>
public class MarketMicrostructureAnalysis
{
    public string Symbol { get; set; } = string.Empty;
    public DateTime AnalyzedAt { get; set; }
    public decimal BidPrice { get; set; }
    public decimal AskPrice { get; set; }
    public decimal Spread { get; set; }
    public decimal SpreadPercent { get; set; }
    public decimal BidSize { get; set; }
    public decimal AskSize { get; set; }
    public decimal OrderBookImbalance { get; set; }
    public decimal TickDirection { get; set; }
    public int RecentTradeCount { get; set; }
    public decimal AverageTradeSize { get; set; }
    public MicrostructureQuality Quality { get; set; }
    public LiquidityLevel LiquidityLevel { get; set; }
}

/// <summary>
/// Spread analysis data
/// </summary>
public class SpreadAnalysis
{
    public string Symbol { get; set; } = string.Empty;
    public DateTime AnalyzedAt { get; set; }
    public decimal CurrentSpread { get; set; }
    public decimal AverageSpread5Min { get; set; }
    public decimal AverageSpread1Hour { get; set; }
    public decimal SpreadVolatility { get; set; }
    public decimal SpreadPercentile { get; set; }
    public bool IsSpreadSpike { get; set; }
    public SpreadTrend Trend { get; set; }
    public SpreadStability Stability { get; set; }
}

/// <summary>
/// VWAP execution analysis
/// </summary>
public class VwapExecutionAnalysis
{
    public string Symbol { get; set; } = string.Empty;
    public DateTime AnalyzedAt { get; set; }
    public decimal CurrentVwap { get; set; }
    public decimal CurrentPrice { get; set; }
    public decimal VwapDeviation { get; set; }
    public decimal VwapTrend { get; set; }
    public VwapExecutionRecommendation Recommendation { get; set; }
    public decimal OptimalExecutionPrice { get; set; }
    public ExecutionTiming OptimalTiming { get; set; }
}

// ExecutionQualityMetrics is defined in IExecutionQAService.cs

/// <summary>
/// Liquidity assessment
/// </summary>
public class LiquidityAssessment
{
    public string Symbol { get; set; } = string.Empty;
    public DateTime AssessedAt { get; set; }
    public decimal BidLiquidity { get; set; }
    public decimal AskLiquidity { get; set; }
    public decimal TotalLiquidity { get; set; }
    public decimal LiquidityScore { get; set; }
    public LiquidityLevel Level { get; set; }
    public decimal EstimatedMarketImpact { get; set; }
    public decimal MaxRecommendedSize { get; set; }
    public LiquidityTrend Trend { get; set; }
}

/// <summary>
/// Execution throttle status
/// </summary>
public class ExecutionThrottleStatus
{
    public string Symbol { get; set; } = string.Empty;
    public bool IsThrottled { get; set; }
    public DateTime ThrottledAt { get; set; }
    public DateTime ExpiresAt { get; set; }
    public string Reason { get; set; } = string.Empty;
    public ThrottleType ThrottleType { get; set; }
    public TimeSpan RemainingDuration { get; set; }
}

/// <summary>
/// Execution parameters for a symbol
/// </summary>
public class ExecutionParameters
{
    public decimal MaxSpreadPercent { get; set; } = 0.5m;
    public decimal MaxSlippageBps { get; set; } = 10m;
    public TimeSpan MaxExecutionTime { get; set; } = TimeSpan.FromSeconds(30);
    public bool AllowMarketOrders { get; set; } = true;
    public bool RequireVwapCheck { get; set; } = true;
    public decimal MinLiquidityThreshold { get; set; } = 10000m;
    public ExecutionAggressiveness Aggressiveness { get; set; } = ExecutionAggressiveness.Moderate;
}

/// <summary>
/// Real-time execution configuration
/// </summary>
public record RealTimeExecutionConfig(
    int MonitoringIntervalMs = 1000,
    decimal SpreadSpikeThreshold = 2.0m,
    decimal LiquidityThreshold = 5000m,
    int MaxConcurrentExecutions = 10,
    TimeSpan DefaultThrottleDuration = default,
    bool EnableAdaptiveExecution = true,
    bool EnableSpreadMonitoring = true,
    bool EnableVwapGuidance = true
)
{
    public RealTimeExecutionConfig() : this(DefaultThrottleDuration: TimeSpan.FromMinutes(5)) { }
}

/// <summary>
/// Execution statistics
/// </summary>
public class ExecutionStatistics
{
    public DateTime LastUpdated { get; set; }
    public int TotalExecutions { get; set; }
    public int ThrottledExecutions { get; set; }
    public decimal AverageSlippage { get; set; }
    public decimal AverageExecutionTime { get; set; }
    public Dictionary<OrderType, int> ExecutionsByOrderType { get; set; } = new();
    public Dictionary<string, ExecutionQualityMetrics> SymbolMetrics { get; set; } = new();
    public TimeSpan MonitoringDuration { get; set; }
}

/// <summary>
/// Order side enumeration
/// </summary>
public enum OrderSide
{
    Buy,
    Sell
}

/// <summary>
/// Order type enumeration
/// </summary>
public enum OrderType
{
    Market,
    Limit,
    StopLimit,
    LimitOnOpen,
    MarketOnClose
}

/// <summary>
/// Execution timing
/// </summary>
public enum ExecutionTiming
{
    Immediate,
    Opportunistic,
    Patient,
    EndOfDay
}

/// <summary>
/// Execution urgency
/// </summary>
public enum ExecutionUrgency
{
    Low,
    Normal,
    High,
    Critical
}

/// <summary>
/// Microstructure quality
/// </summary>
public enum MicrostructureQuality
{
    Excellent,
    Good,
    Fair,
    Poor,
    VeryPoor
}

/// <summary>
/// Liquidity level
/// </summary>
public enum LiquidityLevel
{
    VeryHigh,
    High,
    Normal,
    Low,
    VeryLow
}

/// <summary>
/// Spread trend
/// </summary>
public enum SpreadTrend
{
    Tightening,
    Stable,
    Widening
}

/// <summary>
/// Spread stability
/// </summary>
public enum SpreadStability
{
    VeryStable,
    Stable,
    Moderate,
    Unstable,
    VeryUnstable
}

/// <summary>
/// VWAP execution recommendation
/// </summary>
public enum VwapExecutionRecommendation
{
    ExecuteAboveVwap,
    ExecuteBelowVwap,
    ExecuteAtVwap,
    WaitForBetterPrice,
    ExecuteImmediately
}

// ExecutionQualityRating is defined in IExecutionQAService.cs

/// <summary>
/// Liquidity trend
/// </summary>
public enum LiquidityTrend
{
    Improving,
    Stable,
    Deteriorating
}

/// <summary>
/// Throttle type
/// </summary>
public enum ThrottleType
{
    SpreadSpike,
    LowLiquidity,
    HighVolatility,
    Manual,
    SystemOverload
}

/// <summary>
/// Execution aggressiveness
/// </summary>
public enum ExecutionAggressiveness
{
    Passive,
    Moderate,
    Aggressive,
    VeryAggressive
}

/// <summary>
/// Real-time execution status
/// </summary>
public enum RealTimeExecutionStatus
{
    Stopped,
    Starting,
    Active,
    Throttled,
    Error
}

/// <summary>
/// Event args for execution decision made
/// </summary>
public class ExecutionDecisionEventArgs : EventArgs
{
    public string Symbol { get; set; } = string.Empty;
    public ExecutionStrategy Strategy { get; set; } = new();
    public DateTime DecisionMadeAt { get; set; }
    public TimeSpan AnalysisTime { get; set; }
}

/// <summary>
/// Event args for execution throttled
/// </summary>
public class ExecutionThrottledEventArgs : EventArgs
{
    public string Symbol { get; set; } = string.Empty;
    public ThrottleType ThrottleType { get; set; }
    public TimeSpan Duration { get; set; }
    public string Reason { get; set; } = string.Empty;
    public DateTime ThrottledAt { get; set; }
}

/// <summary>
/// Event args for spread spike detected
/// </summary>
public class SpreadSpikeEventArgs : EventArgs
{
    public string Symbol { get; set; } = string.Empty;
    public decimal CurrentSpread { get; set; }
    public decimal NormalSpread { get; set; }
    public decimal SpikeMultiplier { get; set; }
    public DateTime DetectedAt { get; set; }
}

/// <summary>
/// Event args for execution conditions changed
/// </summary>
public class ExecutionConditionsChangedEventArgs : EventArgs
{
    public string Symbol { get; set; } = string.Empty;
    public ExecutionConditionType ConditionType { get; set; }
    public string PreviousValue { get; set; } = string.Empty;
    public string NewValue { get; set; } = string.Empty;
    public DateTime ChangedAt { get; set; }
}

/// <summary>
/// Execution condition type
/// </summary>
public enum ExecutionConditionType
{
    SpreadChange,
    LiquidityChange,
    VwapDeviation,
    VolatilityChange,
    OrderBookImbalance
}
