using SmaTrendFollower.Models;
using Alpaca.Markets;
using Microsoft.Extensions.Logging;

namespace SmaTrendFollower.Services;

/// <summary>
/// Enhanced trade executor with smart entry/exit timing
/// </summary>
public interface ISmartTradeExecutor : ITradeExecutor
{
    /// <summary>
    /// Executes trade with smart timing and VWAP-based pricing
    /// </summary>
    Task<bool> ExecuteSmartTradeAsync(TradingSignal signal, decimal quantity);
    
    /// <summary>
    /// Calculates optimal entry price based on VWAP and market conditions
    /// </summary>
    Task<decimal> CalculateOptimalEntryPriceAsync(string symbol, decimal currentPrice);
}

/// <summary>
/// Smart trade executor implementation
/// </summary>
public sealed class SmartTradeExecutor : ISmartTradeExecutor
{
    private readonly ITradeExecutor _baseExecutor;
    private readonly IMarketDataService _marketDataService;
    private readonly IAlpacaClientFactory _clientFactory;
    private readonly ILogger<SmartTradeExecutor> _logger;

    // Trading time windows (EST)
    private static readonly TimeSpan MarketOpen = new(9, 30, 0);
    private static readonly TimeSpan OptimalEntryStart = new(10, 0, 0);
    private static readonly TimeSpan OptimalEntryEnd = new(11, 0, 0);
    private static readonly TimeSpan MarketClose = new(16, 0, 0);

    public SmartTradeExecutor(
        ITradeExecutor baseExecutor,
        IMarketDataService marketDataService,
        IAlpacaClientFactory clientFactory,
        ILogger<SmartTradeExecutor> logger)
    {
        _baseExecutor = baseExecutor;
        _marketDataService = marketDataService;
        _clientFactory = clientFactory;
        _logger = logger;
    }

    public async Task ExecuteTradeAsync(TradingSignal signal, decimal quantity)
    {
        // Use smart execution by default
        await ExecuteSmartTradeAsync(signal, quantity);
    }

    public async Task<bool> ExecuteSmartTradeAsync(TradingSignal signal, decimal quantity)
    {
        try
        {
            _logger.LogInformation("Executing smart trade for {Symbol}: Quantity={Quantity}", 
                signal.Symbol, quantity);

            // Check if we're in optimal trading window
            var currentTime = DateTime.Now.TimeOfDay;
            var isOptimalTime = IsOptimalTradingTime(currentTime);

            if (!isOptimalTime)
            {
                _logger.LogInformation("Outside optimal trading window for {Symbol}, using market order",
                    signal.Symbol);
                await _baseExecutor.ExecuteTradeAsync(signal, quantity);
                return true;
            }

            // Calculate optimal entry price
            var optimalPrice = await CalculateOptimalEntryPriceAsync(signal.Symbol, signal.Price);
            
            // Use limit order with optimal pricing
            return await ExecuteLimitOrderAsync(signal, quantity, optimalPrice);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in smart trade execution for {Symbol}", signal.Symbol);

            // Fallback to basic execution
            await _baseExecutor.ExecuteTradeAsync(signal, quantity);
            return false;
        }
    }

    public async Task<decimal> CalculateOptimalEntryPriceAsync(string symbol, decimal currentPrice)
    {
        try
        {
            // Get recent intraday data for VWAP calculation
            var endTime = DateTime.UtcNow;
            var startTime = endTime.AddHours(-2); // Last 2 hours

            var minuteBars = await _marketDataService.GetStockMinuteBarsAsync(symbol, startTime, endTime);
            var bars = minuteBars.Items.ToList();

            if (bars.Count < 10)
            {
                _logger.LogWarning("Insufficient intraday data for {Symbol}, using current price", symbol);
                return currentPrice;
            }

            // Calculate VWAP
            var vwap = CalculateVwap(bars);
            
            // Calculate bid-ask spread estimate (using recent price volatility)
            var spreadEstimate = CalculateSpreadEstimate(bars);
            
            // Determine optimal entry price
            var optimalPrice = CalculateOptimalPrice(currentPrice, vwap, spreadEstimate);
            
            _logger.LogInformation("Optimal pricing for {Symbol}: Current=${Current:F2}, " +
                                 "VWAP=${VWAP:F2}, Optimal=${Optimal:F2}",
                symbol, currentPrice, vwap, optimalPrice);

            return optimalPrice;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error calculating optimal entry price for {Symbol}", symbol);
            return currentPrice;
        }
    }

    private async Task<bool> ExecuteLimitOrderAsync(TradingSignal signal, decimal quantity, decimal limitPrice)
    {
        try
        {
            using var tradingClient = _clientFactory.CreateTradingClient();

            // Create limit order request
            var limitOrderRequest = new NewOrderRequest(signal.Symbol, (int)Math.Ceiling(quantity), Alpaca.Markets.OrderSide.Buy, OrderType.Limit, TimeInForce.Day)
            {
                LimitPrice = limitPrice
            };

            _logger.LogInformation("Placing limit order for {Symbol}: Quantity={Quantity}, " +
                                 "Limit=${Limit:F2}", signal.Symbol, quantity, limitPrice);

            var order = await tradingClient.PostOrderAsync(limitOrderRequest);
            
            _logger.LogInformation("Limit order placed for {Symbol}: OrderId={OrderId}, " +
                                 "Status={Status}", signal.Symbol, order.OrderId, order.OrderStatus);

            // Monitor order for a short period
            await MonitorOrderAsync(order.OrderId, TimeSpan.FromMinutes(5));

            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error placing limit order for {Symbol}", signal.Symbol);
            
            // Fallback to market order
            _logger.LogInformation("Falling back to market order for {Symbol}", signal.Symbol);
            await _baseExecutor.ExecuteTradeAsync(signal, quantity);
            return false;
        }
    }

    private async Task MonitorOrderAsync(Guid orderId, TimeSpan timeout)
    {
        try
        {
            using var tradingClient = _clientFactory.CreateTradingClient();
            var endTime = DateTime.UtcNow.Add(timeout);

            while (DateTime.UtcNow < endTime)
            {
                var order = await tradingClient.GetOrderAsync(orderId);
                
                if (order.OrderStatus == OrderStatus.Filled)
                {
                    _logger.LogInformation("Limit order filled: OrderId={OrderId}, " +
                                         "FilledPrice=${Price:F2}", orderId, order.AverageFillPrice ?? 0);
                    return;
                }
                
                if (order.OrderStatus == OrderStatus.Canceled || 
                    order.OrderStatus == OrderStatus.Rejected)
                {
                    _logger.LogWarning("Limit order not filled: OrderId={OrderId}, Status={Status}", 
                        orderId, order.OrderStatus);
                    return;
                }

                await Task.Delay(TimeSpan.FromSeconds(30)); // Check every 30 seconds
            }

            _logger.LogInformation("Order monitoring timeout reached for OrderId={OrderId}", orderId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error monitoring order {OrderId}", orderId);
        }
    }

    private bool IsOptimalTradingTime(TimeSpan currentTime)
    {
        // Optimal trading window: 10:00 AM - 11:00 AM EST
        return currentTime >= OptimalEntryStart && currentTime <= OptimalEntryEnd;
    }

    private decimal CalculateVwap(IEnumerable<IBar> bars)
    {
        var barsList = bars.ToList();
        if (!barsList.Any()) return 0;

        var totalVolume = 0m;
        var totalVolumePrice = 0m;

        foreach (var bar in barsList)
        {
            var typicalPrice = (bar.High + bar.Low + bar.Close) / 3;
            var volume = (decimal)bar.Volume;
            
            totalVolumePrice += typicalPrice * volume;
            totalVolume += volume;
        }

        return totalVolume > 0 ? totalVolumePrice / totalVolume : 0;
    }

    private decimal CalculateSpreadEstimate(IEnumerable<IBar> bars)
    {
        var barsList = bars.ToList();
        if (barsList.Count < 5) return 0.01m; // Default 1 cent spread

        // Estimate spread from high-low ranges
        var avgRange = barsList.Average(b => b.High - b.Low);
        return (decimal)avgRange * 0.5m; // Assume spread is ~50% of average range
    }

    private decimal CalculateOptimalPrice(decimal currentPrice, decimal vwap, decimal spreadEstimate)
    {
        // Start with VWAP as base
        var basePrice = vwap;
        
        // If current price is significantly above VWAP, use current price
        if (currentPrice > vwap * 1.005m) // 0.5% above VWAP
        {
            basePrice = currentPrice;
        }

        // Adjust for spread - try to get filled by being slightly aggressive
        var optimalPrice = basePrice + (spreadEstimate * 0.3m);
        
        // Don't pay more than 0.2% above current price
        var maxPrice = currentPrice * 1.002m;
        
        return Math.Min(optimalPrice, maxPrice);
    }
}
