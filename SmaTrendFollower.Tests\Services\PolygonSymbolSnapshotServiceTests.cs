using FluentAssertions;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Moq;
using SmaTrendFollower.Models;
using SmaTrendFollower.Services;
using System.Net;
using System.Text.Json;
using Xunit;

namespace SmaTrendFollower.Tests.Services;

public class PolygonSymbolSnapshotServiceTests
{
    private readonly Mock<IPolygonClientFactory> _mockPolygonFactory;
    private readonly Mock<HttpClient> _mockHttpClient;
    private readonly Mock<IPolygonRateLimitHelper> _mockRateLimitHelper;
    private readonly Mock<IConfiguration> _mockConfiguration;
    private readonly Mock<ILogger<PolygonSymbolSnapshotService>> _mockLogger;
    private readonly PolygonSymbolSnapshotService _service;

    public PolygonSymbolSnapshotServiceTests()
    {
        _mockPolygonFactory = new Mock<IPolygonClientFactory>();
        _mockHttpClient = new Mock<HttpClient>();
        _mockRateLimitHelper = new Mock<IPolygonRateLimitHelper>();
        _mockConfiguration = new Mock<IConfiguration>();
        _mockLogger = new Mock<ILogger<PolygonSymbolSnapshotService>>();

        // Setup polygon factory
        _mockPolygonFactory.Setup(f => f.CreateClient()).Returns(_mockHttpClient.Object);
        _mockPolygonFactory.Setup(f => f.GetRateLimitHelper()).Returns(_mockRateLimitHelper.Object);
        _mockPolygonFactory.Setup(f => f.AddApiKeyToUrl(It.IsAny<string>())).Returns<string>(url => $"{url}?apikey=test");

        _service = new PolygonSymbolSnapshotService(
            _mockPolygonFactory.Object,
            _mockConfiguration.Object,
            _mockLogger.Object);
    }

    [Fact]
    public async Task GetAllSnapshotsAsync_ShouldReturnSnapshots_WhenApiReturnsValidData()
    {
        // Arrange
        var mockResponse = new PolygonSnapshotResponse
        {
            Status = "OK",
            Count = 2,
            Results = new List<PolygonSnapshotTicker>
            {
                new()
                {
                    Ticker = "AAPL",
                    LastTrade = new PolygonTrade { Price = 150.00m },
                    TodaysChangePerc = 1.5m
                },
                new()
                {
                    Ticker = "MSFT",
                    LastTrade = new PolygonTrade { Price = 300.00m },
                    TodaysChangePerc = 2.0m
                }
            }
        };

        var httpResponse = new HttpResponseMessage(HttpStatusCode.OK)
        {
            Content = new StringContent(JsonSerializer.Serialize(mockResponse))
        };

        _mockRateLimitHelper
            .Setup(r => r.ExecuteAsync(It.IsAny<Func<Task<HttpResponseMessage>>>(), It.IsAny<string>()))
            .ReturnsAsync(httpResponse);

        // Act
        var result = await _service.GetAllSnapshotsAsync();

        // Assert
        result.Should().HaveCount(2);
        result.Should().Contain(s => s.Ticker == "AAPL");
        result.Should().Contain(s => s.Ticker == "MSFT");
    }

    [Fact]
    public async Task GetSnapshotsAsync_ShouldFilterByRequestedSymbols()
    {
        // Arrange
        var requestedSymbols = new[] { "AAPL" };
        var mockResponse = new PolygonSnapshotResponse
        {
            Status = "OK",
            Count = 2,
            Results = new List<PolygonSnapshotTicker>
            {
                new()
                {
                    Ticker = "AAPL",
                    LastTrade = new PolygonTrade { Price = 150.00m },
                    TodaysChangePerc = 1.5m
                },
                new()
                {
                    Ticker = "MSFT",
                    LastTrade = new PolygonTrade { Price = 300.00m },
                    TodaysChangePerc = 2.0m
                }
            }
        };

        var httpResponse = new HttpResponseMessage(HttpStatusCode.OK)
        {
            Content = new StringContent(JsonSerializer.Serialize(mockResponse))
        };

        _mockRateLimitHelper
            .Setup(r => r.ExecuteAsync(It.IsAny<Func<Task<HttpResponseMessage>>>(), It.IsAny<string>()))
            .ReturnsAsync(httpResponse);

        // Act
        var result = await _service.GetSnapshotsAsync(requestedSymbols);

        // Assert
        result.Should().HaveCount(1);
        result.Should().Contain(s => s.Ticker == "AAPL");
        result.Should().NotContain(s => s.Ticker == "MSFT");
    }

    [Fact]
    public async Task FilterAndRankSymbolsAsync_ShouldApplyFilters_AndReturnRankedCandidates()
    {
        // Arrange
        var symbols = new List<PolygonSymbolInfo>
        {
            new() { Ticker = "AAPL", Name = "Apple Inc.", Market = "stocks", Active = true, Type = "CS" },
            new() { Ticker = "MSFT", Name = "Microsoft Corp.", Market = "stocks", Active = true, Type = "CS" },
            new() { Ticker = "LOWPRICE", Name = "Low Price Corp.", Market = "stocks", Active = true, Type = "CS" }
        };

        var criteria = new CandidateFilterCriteria
        {
            MinPrice = 100.0m,
            MinAverageVolume = 1_000_000,
            MinVolatilityPercent = 2.0m,
            MaxCandidates = 10
        };

        // Mock snapshots
        var snapshotResponse = new PolygonSnapshotResponse
        {
            Status = "OK",
            Count = 3,
            Results = new List<PolygonSnapshotTicker>
            {
                new()
                {
                    Ticker = "AAPL",
                    LastTrade = new PolygonTrade { Price = 150.00m },
                    TodaysChangePerc = 1.5m
                },
                new()
                {
                    Ticker = "MSFT",
                    LastTrade = new PolygonTrade { Price = 300.00m },
                    TodaysChangePerc = 2.0m
                },
                new()
                {
                    Ticker = "LOWPRICE",
                    LastTrade = new PolygonTrade { Price = 5.00m }, // Below minimum price
                    TodaysChangePerc = 0.5m
                }
            }
        };

        // Mock historical data responses for volatility and volume
        var historicalResponse = new PolygonAggregatesResponse
        {
            Status = "OK",
            Count = 20,
            Results = GenerateMockHistoricalBars(20, 150.0m, 2_000_000) // High volume, good volatility
        };

        var lowVolumeResponse = new PolygonAggregatesResponse
        {
            Status = "OK",
            Count = 20,
            Results = GenerateMockHistoricalBars(20, 5.0m, 500_000) // Low volume
        };

        var snapshotHttpResponse = new HttpResponseMessage(HttpStatusCode.OK)
        {
            Content = new StringContent(JsonSerializer.Serialize(snapshotResponse))
        };

        var historicalHttpResponse = new HttpResponseMessage(HttpStatusCode.OK)
        {
            Content = new StringContent(JsonSerializer.Serialize(historicalResponse))
        };

        var lowVolumeHttpResponse = new HttpResponseMessage(HttpStatusCode.OK)
        {
            Content = new StringContent(JsonSerializer.Serialize(lowVolumeResponse))
        };

        _mockRateLimitHelper
            .SetupSequence(r => r.ExecuteAsync(It.IsAny<Func<Task<HttpResponseMessage>>>(), It.IsAny<string>()))
            .ReturnsAsync(snapshotHttpResponse) // Snapshots call
            .ReturnsAsync(historicalHttpResponse) // AAPL volatility
            .ReturnsAsync(historicalHttpResponse) // MSFT volatility
            .ReturnsAsync(lowVolumeHttpResponse) // LOWPRICE volatility
            .ReturnsAsync(historicalHttpResponse) // AAPL volume
            .ReturnsAsync(historicalHttpResponse) // MSFT volume
            .ReturnsAsync(lowVolumeHttpResponse); // LOWPRICE volume

        // Act
        var result = await _service.FilterAndRankSymbolsAsync(symbols, criteria);

        // Assert
        var candidates = result.ToList();
        candidates.Should().HaveCount(2); // AAPL and MSFT should pass, LOWPRICE should fail price filter
        candidates.Should().Contain(c => c.Symbol == "AAPL");
        candidates.Should().Contain(c => c.Symbol == "MSFT");
        candidates.Should().NotContain(c => c.Symbol == "LOWPRICE");
        candidates.Should().BeInDescendingOrder(c => c.RankingScore);
    }

    [Fact]
    public async Task GetHistoricalVolatilityAsync_ShouldCalculateVolatility_ForValidSymbols()
    {
        // Arrange
        var symbols = new[] { "AAPL", "MSFT" };
        var mockResponse = new PolygonAggregatesResponse
        {
            Status = "OK",
            Count = 20,
            Results = GenerateMockHistoricalBars(20, 150.0m, 1_000_000)
        };

        var httpResponse = new HttpResponseMessage(HttpStatusCode.OK)
        {
            Content = new StringContent(JsonSerializer.Serialize(mockResponse))
        };

        _mockRateLimitHelper
            .Setup(r => r.ExecuteAsync(It.IsAny<Func<Task<HttpResponseMessage>>>(), It.IsAny<string>()))
            .ReturnsAsync(httpResponse);

        // Act
        var result = await _service.GetHistoricalVolatilityAsync(symbols);

        // Assert
        result.Should().HaveCount(2);
        result.Should().ContainKey("AAPL");
        result.Should().ContainKey("MSFT");
        result.Values.Should().AllSatisfy(v => v.Should().BeGreaterThan(0));
    }

    [Fact]
    public async Task GetAverageVolumeAsync_ShouldCalculateAverageVolume_ForValidSymbols()
    {
        // Arrange
        var symbols = new[] { "AAPL", "MSFT" };
        var mockResponse = new PolygonAggregatesResponse
        {
            Status = "OK",
            Count = 20,
            Results = GenerateMockHistoricalBars(20, 150.0m, 2_000_000)
        };

        var httpResponse = new HttpResponseMessage(HttpStatusCode.OK)
        {
            Content = new StringContent(JsonSerializer.Serialize(mockResponse))
        };

        _mockRateLimitHelper
            .Setup(r => r.ExecuteAsync(It.IsAny<Func<Task<HttpResponseMessage>>>(), It.IsAny<string>()))
            .ReturnsAsync(httpResponse);

        // Act
        var result = await _service.GetAverageVolumeAsync(symbols);

        // Assert
        result.Should().HaveCount(2);
        result.Should().ContainKey("AAPL");
        result.Should().ContainKey("MSFT");
        result.Values.Should().AllSatisfy(v => v.Should().BeGreaterThan(0));
    }

    [Fact]
    public async Task GetAllSnapshotsAsync_ShouldHandleApiError_Gracefully()
    {
        // Arrange
        var httpResponse = new HttpResponseMessage(HttpStatusCode.InternalServerError);

        _mockRateLimitHelper
            .Setup(r => r.ExecuteAsync(It.IsAny<Func<Task<HttpResponseMessage>>>(), It.IsAny<string>()))
            .ReturnsAsync(httpResponse);

        // Act
        var result = await _service.GetAllSnapshotsAsync();

        // Assert
        result.Should().BeEmpty();
    }

    [Fact]
    public async Task GetSnapshotsAsync_ShouldReturnEmpty_WhenNoSymbolsProvided()
    {
        // Act
        var result = await _service.GetSnapshotsAsync(Array.Empty<string>());

        // Assert
        result.Should().BeEmpty();
    }

    private static List<PolygonBar> GenerateMockHistoricalBars(int count, decimal basePrice, long baseVolume)
    {
        var bars = new List<PolygonBar>();
        var random = new Random(42); // Fixed seed for consistent tests

        for (int i = 0; i < count; i++)
        {
            var priceVariation = (decimal)(random.NextDouble() * 0.1 - 0.05); // ±5% variation
            var volumeVariation = (long)(random.NextDouble() * baseVolume * 0.5); // ±50% variation

            bars.Add(new PolygonBar
            {
                Close = basePrice * (1 + priceVariation),
                Volume = baseVolume + volumeVariation,
                Timestamp = DateTimeOffset.UtcNow.AddDays(-count + i).ToUnixTimeMilliseconds()
            });
        }

        return bars;
    }
}
