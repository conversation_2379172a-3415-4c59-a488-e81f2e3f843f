using SmaTrendFollower.Models;

namespace SmaTrendFollower.Services;

/// <summary>
/// Slippage estimation service for modeling expected vs actual entry fill prices
/// Learns and applies average expected fill distance by symbol and spread
/// Helps optimize order sizing and execution strategies
/// </summary>
public interface ISlippageEstimator : IDisposable
{
    // === Events ===
    
    /// <summary>
    /// Fired when slippage data is updated for a symbol
    /// </summary>
    event EventHandler<SlippageUpdatedEventArgs>? SlippageUpdated;
    
    /// <summary>
    /// Fired when significant slippage is detected
    /// </summary>
    event EventHandler<HighSlippageDetectedEventArgs>? HighSlippageDetected;
    
    /// <summary>
    /// Fired when slippage model is recalibrated
    /// </summary>
    event EventHandler<SlippageModelRecalibratedEventArgs>? ModelRecalibrated;
    
    // === Core Methods ===
    
    /// <summary>
    /// Start slippage tracking for specified symbols
    /// </summary>
    Task StartTrackingAsync(IEnumerable<string> symbols, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Stop slippage tracking
    /// </summary>
    Task StopTrackingAsync(CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Record an order submission for slippage tracking
    /// </summary>
    Task RecordOrderSubmissionAsync(OrderSubmissionRecord submission);
    
    /// <summary>
    /// Record an order fill for slippage calculation
    /// </summary>
    Task RecordOrderFillAsync(OrderFillRecord fill);
    
    /// <summary>
    /// Estimate expected slippage for a symbol and order size
    /// </summary>
    Task<SlippageEstimate> EstimateSlippageAsync(string symbol, decimal orderSize, OrderSide side, decimal currentSpread);
    
    /// <summary>
    /// Get historical slippage statistics for a symbol
    /// </summary>
    Task<SlippageStatistics?> GetSlippageStatisticsAsync(string symbol);
    
    /// <summary>
    /// Get slippage model parameters for a symbol
    /// </summary>
    Task<SlippageModel?> GetSlippageModelAsync(string symbol);
    
    /// <summary>
    /// Recalibrate slippage model for a symbol using recent data
    /// </summary>
    Task RecalibrateModelAsync(string symbol);
    
    /// <summary>
    /// Get recent slippage events for a symbol
    /// </summary>
    Task<IEnumerable<SlippageEvent>> GetRecentSlippageEventsAsync(string symbol, int hours = 24);
    
    // === Configuration ===
    
    /// <summary>
    /// Update slippage estimation configuration
    /// </summary>
    Task UpdateConfigurationAsync(SlippageEstimatorConfig config);
    
    /// <summary>
    /// Get tracking status
    /// </summary>
    SlippageTrackingStatus GetStatus();
    
    /// <summary>
    /// Get list of tracked symbols
    /// </summary>
    IEnumerable<string> GetTrackedSymbols();
}

/// <summary>
/// Slippage estimator configuration
/// </summary>
public record SlippageEstimatorConfig(
    int HistoryDays = 30,
    int MinSamplesForModel = 10,
    decimal HighSlippageThreshold = 0.05m, // 5 basis points
    decimal ModelRecalibrationThreshold = 0.02m, // 2% change in average slippage
    bool EnableRealTimeUpdates = true,
    bool EnableSpreadAdjustment = true,
    bool EnableVolumeAdjustment = true,
    TimeSpan CacheExpiry = default,
    decimal OutlierFilterThreshold = 3.0m // Z-score threshold for outlier filtering
)
{
    public TimeSpan CacheExpiry { get; init; } = CacheExpiry == default ? TimeSpan.FromMinutes(10) : CacheExpiry;
}

/// <summary>
/// Order submission record for slippage tracking
/// </summary>
public record OrderSubmissionRecord(
    string OrderId,
    string Symbol,
    OrderSide Side,
    decimal Quantity,
    decimal LimitPrice,
    decimal MarketPrice,
    decimal BidPrice,
    decimal AskPrice,
    decimal Spread,
    DateTime SubmissionTime,
    string OrderType
);

/// <summary>
/// Order fill record for slippage calculation
/// </summary>
public record OrderFillRecord(
    string OrderId,
    string Symbol,
    OrderSide Side,
    decimal FilledQuantity,
    decimal FillPrice,
    DateTime FillTime,
    string Exchange,
    decimal Commission
);

/// <summary>
/// Slippage estimate for an order
/// </summary>
public record SlippageEstimate(
    string Symbol,
    decimal ExpectedSlippageBps,
    decimal ExpectedSlippageDollar,
    decimal ConfidenceLevel,
    SlippageReason PrimaryReason,
    DateTime EstimateTime,
    SlippageModelMetrics ModelMetrics
);

/// <summary>
/// Slippage statistics for a symbol
/// </summary>
public record SlippageStatistics(
    string Symbol,
    decimal AverageSlippageBps,
    decimal MedianSlippageBps,
    decimal StandardDeviationBps,
    decimal MaxSlippageBps,
    decimal MinSlippageBps,
    int SampleCount,
    DateTime LastUpdate,
    decimal AverageSpread,
    decimal AverageOrderSize
);

/// <summary>
/// Slippage model for a symbol
/// </summary>
public record SlippageModel(
    string Symbol,
    decimal BaseSlippageBps,
    decimal SpreadMultiplier,
    decimal VolumeImpactFactor,
    decimal VolatilityAdjustment,
    decimal ModelAccuracy,
    DateTime LastCalibration,
    int TrainingDataPoints
);

/// <summary>
/// Slippage event record
/// </summary>
public record SlippageEvent(
    string OrderId,
    string Symbol,
    OrderSide Side,
    decimal ActualSlippageBps,
    decimal ExpectedSlippageBps,
    decimal SlippageDifference,
    DateTime EventTime,
    SlippageReason Reason
);

/// <summary>
/// Slippage model metrics
/// </summary>
public record SlippageModelMetrics(
    decimal Accuracy,
    decimal MeanAbsoluteError,
    decimal RSquared,
    int DataPoints,
    DateTime LastUpdate
);

// OrderSide is defined in IRealTimeExecutionService.cs

/// <summary>
/// Slippage reason classification
/// </summary>
public enum SlippageReason
{
    Normal,
    WideSpread,
    LowLiquidity,
    HighVolatility,
    LargeOrderSize,
    MarketImpact,
    TimingDelay,
    Unknown
}

/// <summary>
/// Slippage tracking status
/// </summary>
public enum SlippageTrackingStatus
{
    Stopped,
    Starting,
    Active,
    Error
}

// === Event Args ===

public class SlippageUpdatedEventArgs : EventArgs
{
    public required string Symbol { get; init; }
    public required SlippageStatistics Statistics { get; init; }
    public required DateTime UpdateTime { get; init; }
}

public class HighSlippageDetectedEventArgs : EventArgs
{
    public required string Symbol { get; init; }
    public required SlippageEvent SlippageEvent { get; init; }
    public required decimal ThresholdBps { get; init; }
    public required DateTime DetectionTime { get; init; }
}

public class SlippageModelRecalibratedEventArgs : EventArgs
{
    public required string Symbol { get; init; }
    public required SlippageModel OldModel { get; init; }
    public required SlippageModel NewModel { get; init; }
    public required DateTime RecalibrationTime { get; init; }
}
