﻿using System;
using System.Collections.Generic;
using System.Linq;

Console.WriteLine("=== SmaTrendFollower Phase 6 Universe Selection Test ===");
Console.WriteLine();

var universeProvider = new MockDynamicUniverseProvider();
var top200Stocks = universeProvider.GetTop200Stocks();

Console.WriteLine($"Selected {top200Stocks.Count} stocks for trading universe:");
Console.WriteLine();

// Display in groups of 10 for readability
for (int i = 0; i < top200Stocks.Count; i += 10)
{
    var group = top200Stocks.Skip(i).Take(10);
    Console.WriteLine($"{i + 1,3}-{Math.Min(i + 10, top200Stocks.Count),3}: {string.Join(", ", group)}");
}

Console.WriteLine();
Console.WriteLine("=== Selection Criteria ===");
Console.WriteLine("• Price > $10.00");
Console.WriteLine("• Average Daily Volume > 1,000,000 shares");
Console.WriteLine("• Market Cap > $1 billion");
Console.WriteLine("• 20-day volatility > 2%");
Console.WriteLine("• Excludes penny stocks, ETFs, and illiquid securities");
Console.WriteLine("• Ranked by 6-month momentum and liquidity");
Console.WriteLine();
Console.WriteLine("=== Phase 6 Enhancements ===");
Console.WriteLine("• Real-time index regime filtering (SPX, VIX, NDX)");
Console.WriteLine("• Market breadth validation");
Console.WriteLine("• VIX data freshness enforcement (15-minute threshold)");
Console.WriteLine("• Dynamic execution strategy selection");
Console.WriteLine("• Breadth-adjusted position sizing");

/// <summary>
/// Mock implementation of DynamicUniverseProvider for demonstration
/// </summary>
public class MockDynamicUniverseProvider
{
    private readonly List<StockCandidate> _stockCandidates;

    public MockDynamicUniverseProvider()
    {
        _stockCandidates = GenerateMockStockCandidates();
    }

    public List<string> GetTop200Stocks()
    {
        return _stockCandidates
            .Where(s => s.Price > 10.00m)
            .Where(s => s.AverageDailyVolume > 1_000_000)
            .Where(s => s.MarketCap > 1_000_000_000m)
            .Where(s => s.Volatility > 0.02m)
            .OrderByDescending(s => s.MomentumScore)
            .ThenByDescending(s => s.LiquidityScore)
            .Take(200)
            .Select(s => s.Symbol)
            .ToList();
    }

    private List<StockCandidate> GenerateMockStockCandidates()
    {
        // This represents the actual stock universe that would be fetched from Polygon
        // In the real system, this data comes from /v3/reference/tickers API
        var candidates = new List<StockCandidate>();

        // Large Cap Tech
        candidates.AddRange(new[]
        {
            new StockCandidate("AAPL", 175.50m, 45_000_000, 2_800_000_000_000m, 0.025m, 0.85m, 0.95m),
            new StockCandidate("MSFT", 378.25m, 25_000_000, 2_800_000_000_000m, 0.022m, 0.82m, 0.92m),
            new StockCandidate("GOOGL", 138.75m, 28_000_000, 1_750_000_000_000m, 0.028m, 0.78m, 0.88m),
            new StockCandidate("AMZN", 145.80m, 35_000_000, 1_500_000_000_000m, 0.032m, 0.75m, 0.85m),
            new StockCandidate("NVDA", 485.20m, 42_000_000, 1_200_000_000_000m, 0.045m, 0.92m, 0.98m),
            new StockCandidate("META", 325.60m, 18_000_000, 850_000_000_000m, 0.035m, 0.72m, 0.82m),
            new StockCandidate("TSLA", 248.75m, 85_000_000, 790_000_000_000m, 0.055m, 0.68m, 0.75m),
            new StockCandidate("NFLX", 445.30m, 8_500_000, 195_000_000_000m, 0.038m, 0.65m, 0.72m),
            new StockCandidate("CRM", 248.60m, 6_500_000, 245_000_000_000m, 0.038m, 0.66m, 0.73m),
            new StockCandidate("ADBE", 568.75m, 2_200_000, 258_000_000_000m, 0.035m, 0.63m, 0.70m),
            new StockCandidate("ORCL", 112.85m, 12_000_000, 315_000_000_000m, 0.025m, 0.60m, 0.67m),
            new StockCandidate("AMD", 138.90m, 45_000_000, 225_000_000_000m, 0.055m, 0.71m, 0.78m),
            new StockCandidate("QCOM", 148.75m, 8_800_000, 165_000_000_000m, 0.038m, 0.57m, 0.64m),
            new StockCandidate("AVGO", 925.40m, 1_800_000, 385_000_000_000m, 0.032m, 0.74m, 0.81m),
            new StockCandidate("TXN", 168.50m, 4_200_000, 152_000_000_000m, 0.028m, 0.56m, 0.63m),
        });

        // Large Cap Finance
        candidates.AddRange(new[]
        {
            new StockCandidate("JPM", 158.90m, 12_000_000, 465_000_000_000m, 0.028m, 0.71m, 0.78m),
            new StockCandidate("BAC", 32.45m, 45_000_000, 260_000_000_000m, 0.032m, 0.69m, 0.76m),
            new StockCandidate("WFC", 42.80m, 28_000_000, 155_000_000_000m, 0.035m, 0.67m, 0.74m),
            new StockCandidate("GS", 385.60m, 2_800_000, 132_000_000_000m, 0.042m, 0.64m, 0.71m),
            new StockCandidate("MS", 88.75m, 8_500_000, 145_000_000_000m, 0.038m, 0.62m, 0.69m),
            new StockCandidate("C", 52.30m, 18_000_000, 98_000_000_000m, 0.045m, 0.58m, 0.65m),
            new StockCandidate("V", 258.75m, 6_500_000, 545_000_000_000m, 0.025m, 0.76m, 0.83m),
            new StockCandidate("MA", 418.90m, 3_200_000, 385_000_000_000m, 0.028m, 0.74m, 0.81m),
            new StockCandidate("AXP", 168.45m, 3_500_000, 125_000_000_000m, 0.032m, 0.61m, 0.68m),
            new StockCandidate("BLK", 785.20m, 650_000, 118_000_000_000m, 0.035m, 0.59m, 0.66m),
        });

        // Large Cap Healthcare
        candidates.AddRange(new[]
        {
            new StockCandidate("JNJ", 162.85m, 8_200_000, 425_000_000_000m, 0.018m, 0.55m, 0.68m),
            new StockCandidate("PFE", 28.95m, 32_000_000, 165_000_000_000m, 0.025m, 0.52m, 0.65m),
            new StockCandidate("UNH", 528.40m, 2_800_000, 495_000_000_000m, 0.022m, 0.73m, 0.85m),
            new StockCandidate("ABBV", 148.75m, 12_500_000, 262_000_000_000m, 0.024m, 0.59m, 0.72m),
            new StockCandidate("MRK", 108.60m, 11_000_000, 275_000_000_000m, 0.021m, 0.56m, 0.69m),
            new StockCandidate("LLY", 598.25m, 3_200_000, 565_000_000_000m, 0.028m, 0.81m, 0.88m),
            new StockCandidate("AMGN", 268.75m, 2_800_000, 145_000_000_000m, 0.032m, 0.52m, 0.59m),
            new StockCandidate("GILD", 78.90m, 8_500_000, 98_000_000_000m, 0.035m, 0.48m, 0.55m),
            new StockCandidate("REGN", 885.60m, 850_000, 95_000_000_000m, 0.042m, 0.61m, 0.68m),
            new StockCandidate("ISRG", 385.75m, 1_800_000, 138_000_000_000m, 0.038m, 0.64m, 0.71m),
        });

        // Continue adding more stocks to reach 200+
        // Consumer Discretionary
        candidates.AddRange(new[]
        {
            new StockCandidate("HD", 328.45m, 3_800_000, 335_000_000_000m, 0.024m, 0.65m, 0.72m),
            new StockCandidate("MCD", 285.75m, 2_800_000, 208_000_000_000m, 0.020m, 0.60m, 0.73m),
            new StockCandidate("NKE", 102.35m, 8_200_000, 158_000_000_000m, 0.032m, 0.53m, 0.66m),
            new StockCandidate("SBUX", 98.75m, 6_500_000, 112_000_000_000m, 0.035m, 0.51m, 0.58m),
            new StockCandidate("TGT", 148.90m, 4_200_000, 68_000_000_000m, 0.042m, 0.48m, 0.55m),
            new StockCandidate("LOW", 218.60m, 3_800_000, 145_000_000_000m, 0.028m, 0.57m, 0.64m),
            new StockCandidate("COST", 685.20m, 2_200_000, 305_000_000_000m, 0.022m, 0.68m, 0.75m),
            new StockCandidate("WMT", 158.20m, 8_500_000, 425_000_000_000m, 0.019m, 0.61m, 0.74m),
        });

        // Energy
        candidates.AddRange(new[]
        {
            new StockCandidate("XOM", 108.75m, 18_000_000, 445_000_000_000m, 0.035m, 0.62m, 0.69m),
            new StockCandidate("CVX", 148.90m, 8_500_000, 285_000_000_000m, 0.032m, 0.59m, 0.66m),
            new StockCandidate("COP", 118.45m, 6_800_000, 145_000_000_000m, 0.042m, 0.56m, 0.63m),
            new StockCandidate("EOG", 128.60m, 4_200_000, 75_000_000_000m, 0.045m, 0.54m, 0.61m),
            new StockCandidate("SLB", 48.75m, 12_000_000, 68_000_000_000m, 0.055m, 0.51m, 0.58m),
            new StockCandidate("PSX", 128.90m, 4_500_000, 58_000_000_000m, 0.048m, 0.49m, 0.56m),
            new StockCandidate("VLO", 138.45m, 3_800_000, 52_000_000_000m, 0.052m, 0.47m, 0.54m),
            new StockCandidate("MPC", 158.75m, 4_200_000, 48_000_000_000m, 0.045m, 0.48m, 0.55m),
        });

        // Industrials
        candidates.AddRange(new[]
        {
            new StockCandidate("BA", 198.75m, 8_500_000, 118_000_000_000m, 0.055m, 0.47m, 0.54m),
            new StockCandidate("CAT", 285.40m, 3_200_000, 148_000_000_000m, 0.038m, 0.58m, 0.65m),
            new StockCandidate("GE", 108.25m, 45_000_000, 118_000_000_000m, 0.042m, 0.55m, 0.62m),
            new StockCandidate("MMM", 98.60m, 4_800_000, 55_000_000_000m, 0.028m, 0.46m, 0.53m),
            new StockCandidate("HON", 198.45m, 2_800_000, 135_000_000_000m, 0.025m, 0.57m, 0.64m),
            new StockCandidate("UPS", 158.90m, 3_500_000, 138_000_000_000m, 0.032m, 0.52m, 0.59m),
            new StockCandidate("RTX", 98.75m, 6_200_000, 145_000_000_000m, 0.035m, 0.49m, 0.56m),
            new StockCandidate("LMT", 458.60m, 1_200_000, 115_000_000_000m, 0.028m, 0.54m, 0.61m),
        });

        // Communication Services
        candidates.AddRange(new[]
        {
            new StockCandidate("VZ", 38.75m, 18_000_000, 162_000_000_000m, 0.022m, 0.48m, 0.55m),
            new StockCandidate("T", 15.85m, 42_000_000, 115_000_000_000m, 0.025m, 0.42m, 0.49m),
            new StockCandidate("CMCSA", 42.60m, 15_000_000, 185_000_000_000m, 0.028m, 0.51m, 0.58m),
            new StockCandidate("DIS", 95.25m, 12_000_000, 175_000_000_000m, 0.045m, 0.49m, 0.56m),
            new StockCandidate("NFLX", 445.30m, 8_500_000, 195_000_000_000m, 0.038m, 0.65m, 0.72m),
            new StockCandidate("CHTR", 385.75m, 1_800_000, 58_000_000_000m, 0.042m, 0.44m, 0.51m),
        });

        // Consumer Staples
        candidates.AddRange(new[]
        {
            new StockCandidate("PG", 152.80m, 6_800_000, 365_000_000_000m, 0.016m, 0.58m, 0.71m),
            new StockCandidate("KO", 59.45m, 15_000_000, 258_000_000_000m, 0.015m, 0.54m, 0.67m),
            new StockCandidate("PEP", 168.90m, 4_500_000, 235_000_000_000m, 0.017m, 0.57m, 0.70m),
            new StockCandidate("MDLZ", 68.25m, 8_200_000, 92_000_000_000m, 0.018m, 0.49m, 0.56m),
            new StockCandidate("CL", 78.90m, 4_500_000, 65_000_000_000m, 0.019m, 0.46m, 0.53m),
            new StockCandidate("KMB", 138.45m, 2_200_000, 45_000_000_000m, 0.021m, 0.43m, 0.50m),
            new StockCandidate("GIS", 68.75m, 3_800_000, 42_000_000_000m, 0.022m, 0.41m, 0.48m),
        });

        // Materials
        candidates.AddRange(new[]
        {
            new StockCandidate("LIN", 398.75m, 1_800_000, 195_000_000_000m, 0.025m, 0.61m, 0.68m),
            new StockCandidate("APD", 268.90m, 1_200_000, 58_000_000_000m, 0.028m, 0.52m, 0.59m),
            new StockCandidate("ECL", 198.45m, 1_500_000, 55_000_000_000m, 0.024m, 0.54m, 0.61m),
            new StockCandidate("FCX", 38.75m, 18_000_000, 55_000_000_000m, 0.065m, 0.48m, 0.55m),
            new StockCandidate("NEM", 42.60m, 8_500_000, 32_000_000_000m, 0.058m, 0.45m, 0.52m),
            new StockCandidate("DOW", 58.90m, 6_200_000, 42_000_000_000m, 0.045m, 0.43m, 0.50m),
        });

        // Utilities
        candidates.AddRange(new[]
        {
            new StockCandidate("NEE", 68.90m, 8_500_000, 138_000_000_000m, 0.022m, 0.52m, 0.59m),
            new StockCandidate("SO", 68.75m, 6_200_000, 72_000_000_000m, 0.018m, 0.48m, 0.55m),
            new StockCandidate("D", 48.25m, 8_800_000, 42_000_000_000m, 0.020m, 0.45m, 0.52m),
            new StockCandidate("DUK", 98.60m, 4_500_000, 75_000_000_000m, 0.019m, 0.47m, 0.54m),
            new StockCandidate("AEP", 88.45m, 3_200_000, 45_000_000_000m, 0.021m, 0.44m, 0.51m),
            new StockCandidate("EXC", 38.75m, 12_000_000, 38_000_000_000m, 0.025m, 0.42m, 0.49m),
        });

        // REITs
        candidates.AddRange(new[]
        {
            new StockCandidate("AMT", 198.75m, 2_800_000, 92_000_000_000m, 0.025m, 0.56m, 0.63m),
            new StockCandidate("PLD", 128.60m, 4_200_000, 118_000_000_000m, 0.028m, 0.58m, 0.65m),
            new StockCandidate("CCI", 108.25m, 3_500_000, 48_000_000_000m, 0.032m, 0.54m, 0.61m),
            new StockCandidate("EQIX", 785.40m, 450_000, 72_000_000_000m, 0.035m, 0.61m, 0.68m),
            new StockCandidate("SPG", 128.90m, 2_800_000, 48_000_000_000m, 0.042m, 0.49m, 0.56m),
            new StockCandidate("PSA", 298.75m, 850_000, 52_000_000_000m, 0.028m, 0.51m, 0.58m),
        });

        // Growth/Tech (Mid-cap)
        candidates.AddRange(new[]
        {
            new StockCandidate("SHOP", 68.75m, 12_000_000, 85_000_000_000m, 0.065m, 0.58m, 0.65m),
            new StockCandidate("SQ", 78.90m, 15_000_000, 45_000_000_000m, 0.072m, 0.55m, 0.62m),
            new StockCandidate("ROKU", 68.25m, 8_500_000, 7_500_000_000m, 0.085m, 0.42m, 0.49m),
            new StockCandidate("UBER", 58.75m, 22_000_000, 115_000_000_000m, 0.055m, 0.52m, 0.59m),
            new StockCandidate("ABNB", 138.90m, 6_500_000, 88_000_000_000m, 0.062m, 0.48m, 0.55m),
            new StockCandidate("DASH", 118.45m, 4_200_000, 42_000_000_000m, 0.075m, 0.44m, 0.51m),
            new StockCandidate("ZM", 68.75m, 12_000_000, 20_500_000_000m, 0.088m, 0.32m, 0.39m),
            new StockCandidate("SNOW", 168.90m, 4_500_000, 52_000_000_000m, 0.095m, 0.38m, 0.45m),
            new StockCandidate("PLTR", 18.75m, 45_000_000, 38_000_000_000m, 0.125m, 0.35m, 0.42m),
            new StockCandidate("RBLX", 42.60m, 18_000_000, 25_000_000_000m, 0.115m, 0.33m, 0.40m),
        });

        // Biotech
        candidates.AddRange(new[]
        {
            new StockCandidate("BIIB", 258.40m, 1_200_000, 37_000_000_000m, 0.055m, 0.44m, 0.51m),
            new StockCandidate("VRTX", 385.75m, 1_800_000, 98_000_000_000m, 0.042m, 0.59m, 0.66m),
            new StockCandidate("CELG", 98.60m, 8_500_000, 68_000_000_000m, 0.048m, 0.46m, 0.53m),
            new StockCandidate("BMRN", 78.90m, 1_200_000, 14_500_000_000m, 0.065m, 0.41m, 0.48m),
            new StockCandidate("ALXN", 168.45m, 2_800_000, 38_000_000_000m, 0.052m, 0.43m, 0.50m),
        });

        // Semiconductors
        candidates.AddRange(new[]
        {
            new StockCandidate("INTC", 43.25m, 28_000_000, 185_000_000_000m, 0.042m, 0.45m, 0.52m),
            new StockCandidate("MU", 68.75m, 18_000_000, 75_000_000_000m, 0.065m, 0.48m, 0.55m),
            new StockCandidate("AMAT", 148.90m, 6_500_000, 125_000_000_000m, 0.045m, 0.54m, 0.61m),
            new StockCandidate("LRCX", 685.40m, 1_200_000, 95_000_000_000m, 0.055m, 0.52m, 0.59m),
            new StockCandidate("KLAC", 485.75m, 1_800_000, 68_000_000_000m, 0.048m, 0.50m, 0.57m),
            new StockCandidate("MRVL", 58.90m, 12_000_000, 48_000_000_000m, 0.058m, 0.47m, 0.54m),
        });

        // Additional quality names to reach 200
        candidates.AddRange(new[]
        {
            new StockCandidate("IBM", 158.75m, 4_500_000, 145_000_000_000m, 0.025m, 0.41m, 0.48m),
            new StockCandidate("CSCO", 48.90m, 18_000_000, 198_000_000_000m, 0.022m, 0.44m, 0.51m),
            new StockCandidate("PYPL", 68.75m, 12_000_000, 78_000_000_000m, 0.055m, 0.46m, 0.53m),
            new StockCandidate("INTU", 568.90m, 1_200_000, 158_000_000_000m, 0.032m, 0.61m, 0.68m),
            new StockCandidate("NOW", 685.45m, 1_800_000, 138_000_000_000m, 0.042m, 0.63m, 0.70m),
            new StockCandidate("PANW", 285.75m, 3_200_000, 88_000_000_000m, 0.048m, 0.58m, 0.65m),
            new StockCandidate("CRWD", 168.90m, 4_500_000, 38_000_000_000m, 0.065m, 0.55m, 0.62m),
            new StockCandidate("ZS", 158.45m, 2_800_000, 22_000_000_000m, 0.075m, 0.51m, 0.58m),
            new StockCandidate("OKTA", 88.75m, 3_500_000, 14_500_000_000m, 0.085m, 0.47m, 0.54m),
            new StockCandidate("DDOG", 98.60m, 4_200_000, 32_000_000_000m, 0.078m, 0.49m, 0.56m),
        });

        return candidates;
    }
}

/// <summary>
/// Represents a stock candidate for universe selection
/// </summary>
public record StockCandidate(
    string Symbol,
    decimal Price,
    long AverageDailyVolume,
    decimal MarketCap,
    decimal Volatility,
    decimal MomentumScore,
    decimal LiquidityScore
);
