using FluentAssertions;
using Microsoft.Extensions.Logging;
using Moq;
using Xunit;
using SmaTrendFollower.Services;
using SmaTrendFollower.Models;
using StackExchange.Redis;
using System.Text.Json;

namespace SmaTrendFollower.Tests.Services;

public class SlippageEstimatorTests : IDisposable
{
    private readonly Mock<ILogger<SlippageEstimator>> _mockLogger;
    private readonly Mock<IDatabase> _mockRedisService;
    private readonly Mock<ITimeProvider> _mockTimeProvider;
    private readonly Mock<IDatabase> _mockDatabase;
    private readonly SlippageEstimator _slippageEstimator;
    private readonly DateTime _fixedTime = new(2024, 1, 15, 10, 30, 0, DateTimeKind.Utc);

    public SlippageEstimatorTests()
    {
        _mockLogger = new Mock<ILogger<SlippageEstimator>>();
        _mockRedisService = new Mock<IRedisService>();
        _mockTimeProvider = new Mock<ITimeProvider>();
        _mockDatabase = new Mock<IDatabase>();

        _mockTimeProvider.Setup(x => x.GetUtcNow()).Returns(_fixedTime);
        _mockRedisService.Setup(x => x.GetDatabaseAsync()).ReturnsAsync(_mockDatabase.Object);
        
        // Setup Redis to return empty values by default
        _mockDatabase.Setup(x => x.StringGetAsync(It.IsAny<RedisKey>(), It.IsAny<CommandFlags>()))
                    .ReturnsAsync(RedisValue.Null);
        _mockDatabase.Setup(x => x.ListRangeAsync(It.IsAny<RedisKey>(), It.IsAny<long>(), It.IsAny<long>(), It.IsAny<CommandFlags>()))
                    .ReturnsAsync(Array.Empty<RedisValue>());

        _slippageEstimator = new SlippageEstimator(_mockLogger.Object, _mockRedisService.Object, _mockTimeProvider.Object);
    }

    [Fact]
    public async Task StartTrackingAsync_WithValidSymbols_ShouldSetStatusToActive()
    {
        // Arrange
        var symbols = new[] { "AAPL", "MSFT" };

        // Act
        await _slippageEstimator.StartTrackingAsync(symbols);

        // Assert
        _slippageEstimator.GetStatus().Should().Be(SlippageTrackingStatus.Active);
        _slippageEstimator.GetTrackedSymbols().Should().BeEquivalentTo(symbols);
    }

    [Fact]
    public async Task StopTrackingAsync_WhenActive_ShouldSetStatusToStopped()
    {
        // Arrange
        var symbols = new[] { "AAPL" };
        await _slippageEstimator.StartTrackingAsync(symbols);

        // Act
        await _slippageEstimator.StopTrackingAsync();

        // Assert
        _slippageEstimator.GetStatus().Should().Be(SlippageTrackingStatus.Stopped);
        _slippageEstimator.GetTrackedSymbols().Should().BeEmpty();
    }

    [Fact]
    public async Task RecordOrderSubmissionAsync_WithValidOrder_ShouldStoreInRedis()
    {
        // Arrange
        var symbol = "AAPL";
        await _slippageEstimator.StartTrackingAsync(new[] { symbol });

        var submission = new OrderSubmissionRecord(
            OrderId: "ORDER123",
            Symbol: symbol,
            Side: OrderSide.Buy,
            Quantity: 100m,
            LimitPrice: 150.00m,
            MarketPrice: 149.95m,
            BidPrice: 149.90m,
            AskPrice: 150.05m,
            Spread: 0.15m,
            SubmissionTime: _fixedTime,
            OrderType: "LIMIT"
        );

        // Act
        await _slippageEstimator.RecordOrderSubmissionAsync(submission);

        // Assert
        _mockDatabase.Verify(x => x.StringSetAsync(
            It.Is<RedisKey>(k => k.ToString().Contains("pending")),
            It.IsAny<RedisValue>(),
            It.IsAny<TimeSpan?>(),
            It.IsAny<When>(),
            It.IsAny<CommandFlags>()), Times.Once);
    }

    [Fact]
    public async Task RecordOrderFillAsync_WithMatchingSubmission_ShouldCalculateSlippage()
    {
        // Arrange
        var symbol = "AAPL";
        await _slippageEstimator.StartTrackingAsync(new[] { symbol });

        var submission = new OrderSubmissionRecord(
            OrderId: "ORDER123",
            Symbol: symbol,
            Side: OrderSide.Buy,
            Quantity: 100m,
            LimitPrice: 150.00m,
            MarketPrice: 149.95m,
            BidPrice: 149.90m,
            AskPrice: 150.05m,
            Spread: 0.15m,
            SubmissionTime: _fixedTime,
            OrderType: "LIMIT"
        );

        var fill = new OrderFillRecord(
            OrderId: "ORDER123",
            Symbol: symbol,
            Side: OrderSide.Buy,
            FilledQuantity: 100m,
            FillPrice: 150.02m, // 2 cents slippage from ask
            FillTime: _fixedTime.AddSeconds(5),
            Exchange: "NASDAQ",
            Commission: 1.00m
        );

        // Setup Redis to return the submission when requested
        var submissionJson = JsonSerializer.Serialize(submission);
        _mockDatabase.Setup(x => x.StringGetAsync(
            It.Is<RedisKey>(k => k.ToString().Contains("pending:ORDER123")),
            It.IsAny<CommandFlags>()))
            .ReturnsAsync(submissionJson);

        // Act
        await _slippageEstimator.RecordOrderSubmissionAsync(submission);
        await _slippageEstimator.RecordOrderFillAsync(fill);

        // Assert
        // Verify that slippage statistics were updated
        _mockDatabase.Verify(x => x.StringSetAsync(
            It.Is<RedisKey>(k => k.ToString().Contains("stats")),
            It.IsAny<RedisValue>(),
            It.IsAny<TimeSpan?>(),
            It.IsAny<When>(),
            It.IsAny<CommandFlags>()), Times.AtLeastOnce);
    }

    [Fact]
    public async Task EstimateSlippageAsync_WithNoModel_ShouldReturnDefaultEstimate()
    {
        // Arrange
        var symbol = "AAPL";
        await _slippageEstimator.StartTrackingAsync(new[] { symbol });

        // Act
        var estimate = await _slippageEstimator.EstimateSlippageAsync(symbol, 100m, OrderSide.Buy, 0.15m);

        // Assert
        estimate.Should().NotBeNull();
        estimate.Symbol.Should().Be(symbol);
        estimate.ExpectedSlippageBps.Should().BeGreaterThan(0);
        estimate.ConfidenceLevel.Should().Be(0.1m); // Low confidence for default estimate
    }

    [Fact]
    public async Task EstimateSlippageAsync_WithExistingModel_ShouldUseModelParameters()
    {
        // Arrange
        var symbol = "AAPL";
        await _slippageEstimator.StartTrackingAsync(new[] { symbol });

        var model = new SlippageModel(
            Symbol: symbol,
            BaseSlippageBps: 10m,
            SpreadMultiplier: 0.5m,
            VolumeImpactFactor: 0.1m,
            VolatilityAdjustment: 2m,
            ModelAccuracy: 0.85m,
            LastCalibration: _fixedTime,
            TrainingDataPoints: 50
        );

        var modelJson = JsonSerializer.Serialize(model);
        _mockDatabase.Setup(x => x.StringGetAsync(
            It.Is<RedisKey>(k => k.ToString().Contains("model")),
            It.IsAny<CommandFlags>()))
            .ReturnsAsync(modelJson);

        // Act
        var estimate = await _slippageEstimator.EstimateSlippageAsync(symbol, 100m, OrderSide.Buy, 0.15m);

        // Assert
        estimate.Should().NotBeNull();
        estimate.Symbol.Should().Be(symbol);
        estimate.ConfidenceLevel.Should().Be(0.85m); // Should match model accuracy
        estimate.ExpectedSlippageBps.Should().BeGreaterThan(10m); // Should be at least base slippage
    }

    [Fact]
    public async Task UpdateConfigurationAsync_WithNewConfig_ShouldUpdateSettings()
    {
        // Arrange
        var newConfig = new SlippageEstimatorConfig(
            HistoryDays: 60,
            MinSamplesForModel: 20,
            HighSlippageThreshold: 0.10m
        );

        // Act
        await _slippageEstimator.UpdateConfigurationAsync(newConfig);

        // Assert
        // Configuration should be updated (verified through behavior in other tests)
        _slippageEstimator.GetStatus().Should().Be(SlippageTrackingStatus.Stopped); // Still stopped until started
    }

    [Fact]
    public async Task RecalibrateModelAsync_WithInsufficientData_ShouldLogWarning()
    {
        // Arrange
        var symbol = "AAPL";
        await _slippageEstimator.StartTrackingAsync(new[] { symbol });

        // Setup Redis to return empty events (insufficient data)
        _mockDatabase.Setup(x => x.ListRangeAsync(
            It.Is<RedisKey>(k => k.ToString().Contains("events")),
            It.IsAny<long>(),
            It.IsAny<long>(),
            It.IsAny<CommandFlags>()))
            .ReturnsAsync(Array.Empty<RedisValue>());

        // Act
        await _slippageEstimator.RecalibrateModelAsync(symbol);

        // Assert
        // Should log warning about insufficient data
        _mockLogger.Verify(
            x => x.Log(
                LogLevel.Warning,
                It.IsAny<EventId>(),
                It.Is<It.IsAnyType>((v, t) => v.ToString()!.Contains("Insufficient data")),
                It.IsAny<Exception>(),
                It.IsAny<Func<It.IsAnyType, Exception?, string>>()),
            Times.Once);
    }

    [Fact]
    public async Task GetSlippageStatisticsAsync_WithCachedData_ShouldReturnStatistics()
    {
        // Arrange
        var symbol = "AAPL";
        var stats = new SlippageStatistics(
            Symbol: symbol,
            AverageSlippageBps: 8.5m,
            MedianSlippageBps: 7.0m,
            StandardDeviationBps: 3.2m,
            MaxSlippageBps: 25.0m,
            MinSlippageBps: 1.0m,
            SampleCount: 100,
            LastUpdate: _fixedTime,
            AverageSpread: 0.12m,
            AverageOrderSize: 150m
        );

        var statsJson = JsonSerializer.Serialize(stats);
        _mockDatabase.Setup(x => x.StringGetAsync(
            It.Is<RedisKey>(k => k.ToString().Contains("stats")),
            It.IsAny<CommandFlags>()))
            .ReturnsAsync(statsJson);

        // Act
        var result = await _slippageEstimator.GetSlippageStatisticsAsync(symbol);

        // Assert
        result.Should().NotBeNull();
        result!.Symbol.Should().Be(symbol);
        result.AverageSlippageBps.Should().Be(8.5m);
        result.SampleCount.Should().Be(100);
    }

    [Fact]
    public async Task GetRecentSlippageEventsAsync_WithTimeFilter_ShouldReturnFilteredEvents()
    {
        // Arrange
        var symbol = "AAPL";
        var recentEvent = new SlippageEvent(
            OrderId: "ORDER1",
            Symbol: symbol,
            Side: OrderSide.Buy,
            ActualSlippageBps: 5.0m,
            ExpectedSlippageBps: 3.0m,
            SlippageDifference: 2.0m,
            EventTime: _fixedTime.AddHours(-1), // Recent
            Reason: SlippageReason.Normal
        );

        var oldEvent = new SlippageEvent(
            OrderId: "ORDER2",
            Symbol: symbol,
            Side: OrderSide.Sell,
            ActualSlippageBps: 8.0m,
            ExpectedSlippageBps: 6.0m,
            SlippageDifference: 2.0m,
            EventTime: _fixedTime.AddDays(-2), // Old
            Reason: SlippageReason.WideSpread
        );

        var events = new[] { JsonSerializer.Serialize(recentEvent), JsonSerializer.Serialize(oldEvent) };
        _mockDatabase.Setup(x => x.ListRangeAsync(
            It.Is<RedisKey>(k => k.ToString().Contains("events")),
            It.IsAny<long>(),
            It.IsAny<long>(),
            It.IsAny<CommandFlags>()))
            .ReturnsAsync(events.Select(e => (RedisValue)e).ToArray());

        // Act
        var result = await _slippageEstimator.GetRecentSlippageEventsAsync(symbol, 24);

        // Assert
        result.Should().HaveCount(1); // Only recent event should be returned
        result.First().OrderId.Should().Be("ORDER1");
    }

    [Theory]
    [InlineData(OrderSide.Buy)]
    [InlineData(OrderSide.Sell)]
    public async Task SlippageCalculation_ForDifferentSides_ShouldCalculateCorrectly(OrderSide side)
    {
        // Arrange
        var symbol = "AAPL";
        await _slippageEstimator.StartTrackingAsync(new[] { symbol });

        var bidPrice = 149.90m;
        var askPrice = 150.05m;
        var expectedPrice = side == OrderSide.Buy ? askPrice : bidPrice;
        var fillPrice = side == OrderSide.Buy ? 150.07m : 149.88m; // Slight slippage

        var submission = new OrderSubmissionRecord(
            OrderId: "ORDER123",
            Symbol: symbol,
            Side: side,
            Quantity: 100m,
            LimitPrice: expectedPrice,
            MarketPrice: expectedPrice,
            BidPrice: bidPrice,
            AskPrice: askPrice,
            Spread: askPrice - bidPrice,
            SubmissionTime: _fixedTime,
            OrderType: "LIMIT"
        );

        var fill = new OrderFillRecord(
            OrderId: "ORDER123",
            Symbol: symbol,
            Side: side,
            FilledQuantity: 100m,
            FillPrice: fillPrice,
            FillTime: _fixedTime.AddSeconds(5),
            Exchange: "NASDAQ",
            Commission: 1.00m
        );

        // Setup Redis to return the submission
        var submissionJson = JsonSerializer.Serialize(submission);
        _mockDatabase.Setup(x => x.StringGetAsync(
            It.Is<RedisKey>(k => k.ToString().Contains("pending")),
            It.IsAny<CommandFlags>()))
            .ReturnsAsync(submissionJson);

        // Act
        await _slippageEstimator.RecordOrderSubmissionAsync(submission);
        await _slippageEstimator.RecordOrderFillAsync(fill);

        // Assert
        // Verify that slippage was calculated and stored
        _mockDatabase.Verify(x => x.ListLeftPushAsync(
            It.Is<RedisKey>(k => k.ToString().Contains("events")),
            It.IsAny<RedisValue>(),
            It.IsAny<When>(),
            It.IsAny<CommandFlags>()), Times.Once);
    }

    public void Dispose()
    {
        _slippageEstimator?.Dispose();
    }
}
